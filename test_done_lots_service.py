#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 DoneLotsDataService 数据服务
验证连接池和基础查询功能
"""

import sys
import io
import os
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_done_lots_service.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestDoneLotsService')

def test_done_lots_service():
    """测试 DoneLotsDataService"""
    try:
        logger.info("🧪 开始测试 DoneLotsDataService...")
        
        # 创建Flask应用上下文
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入并测试数据服务
            from app.services.done_lots_data_service import DoneLotsDataService
            
            service = DoneLotsDataService()
            logger.info("✅ DoneLotsDataService 创建成功")
            
            # 测试1: 获取基础数据（带分页）
            logger.info("🧪 测试1: 获取基础数据（分页）")
            result1 = service.get_done_lots_data(
                pagination={'page': 1, 'size': 10}
            )
            
            if result1['success']:
                logger.info(f"✅ 基础数据查询成功: {len(result1['data'])}条记录，总数: {result1['total_count']}")
                if result1['data']:
                    sample = result1['data'][0]
                    logger.info(f"📊 示例记录: LOT_ID={sample.get('LOT_ID')}, DEVICE={sample.get('DEVICE')}")
            else:
                logger.error(f"❌ 基础数据查询失败: {result1.get('error')}")
            
            # 测试2: 获取数据统计
            logger.info("🧪 测试2: 获取数据统计")
            result2 = service.get_data_statistics()
            
            if result2['success']:
                stats = result2['statistics']
                logger.info(f"✅ 数据统计成功: 总批次={stats.get('total_lots')}, 总数量={stats.get('total_quantity')}")
            else:
                logger.error(f"❌ 数据统计失败: {result2.get('error')}")
            
            # 测试3: 带筛选条件的查询
            logger.info("🧪 测试3: 带筛选条件的查询")
            test_filters = {
                'global_search': 'L',  # 搜索包含L的记录
                'priority_range': [1, 100]
            }
            result3 = service.get_done_lots_data(
                filters=test_filters,
                pagination={'page': 1, 'size': 5}
            )
            
            if result3['success']:
                logger.info(f"✅ 筛选查询成功: {len(result3['data'])}条记录，筛选条件数: {result3['filters_applied']}")
            else:
                logger.error(f"❌ 筛选查询失败: {result3.get('error')}")
            
            # 测试4: 获取失败批次数据
            logger.info("🧪 测试4: 获取失败批次数据")
            result4 = service.get_failed_lots_data(
                filters={'hours_limit': 168}  # 最近一周
            )
            
            if result4['success']:
                logger.info(f"✅ 失败批次查询成功: {len(result4['data'])}条记录")
            else:
                logger.error(f"❌ 失败批次查询失败: {result4.get('error')}")
            
            # 测试5: 获取最终结果数据
            logger.info("🧪 测试5: 获取最终结果数据") 
            result5 = service.get_final_result_data(
                pagination={'page': 1, 'size': 5}
            )
            
            if result5['success']:
                logger.info(f"✅ 最终结果查询成功: {len(result5['data'])}条记录")
            else:
                logger.info(f"ℹ️ 最终结果查询: {result5.get('message', '表可能不存在')}")
            
            # 测试6: 导出功能
            logger.info("🧪 测试6: 导出功能")
            result6 = service.export_data(
                export_type='filtered',
                filters={'priority_range': [1, 10]},
                format='excel'
            )
            
            if result6['success']:
                logger.info(f"✅ 导出功能测试成功: {result6['record_count']}条记录")
            else:
                logger.error(f"❌ 导出功能测试失败: {result6.get('error')}")
            
            logger.info("🎉 DoneLotsDataService 测试完成！")
            return True
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_done_lots_service()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")

if __name__ == "__main__":
    main()