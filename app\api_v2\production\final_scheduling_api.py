#!/usr/bin/env python3
"""
最终排产调整结果 API
用于处理Done Lots页面的调整结果保存、查询、发布等功能
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import text
from app import db
import logging
from datetime import datetime
import json
from dateutil import parser as date_parser

# 创建蓝图
final_scheduling_api = Blueprint('final_scheduling_api', __name__)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_datetime(date_str):
    """
    解析日期字符串为datetime对象
    支持多种格式，包括GMT格式
    """
    if not date_str:
        return None
    
    if isinstance(date_str, datetime):
        return date_str
    
    try:
        # 尝试解析各种日期格式
        return date_parser.parse(date_str)
    except (ValueError, TypeError) as e:
        logger.warning(f"⚠️ 日期解析失败: {date_str}, 错误: {e}")
        return None

@final_scheduling_api.route('/api/v2/production/final-scheduling/save', methods=['POST'])
@login_required
def save_adjustment_results():
    """
    保存调整结果到final_scheduling_result表
    
    请求格式：
    {
        "session_id": "adjust_1752556996616",
        "session_name": "调整会话 2025-01-15",
        "adjustments": [
            {
                "lot_id": "LOT001",
                "original_priority": 1,
                "original_handler_id": "H001",
                "final_priority": 2,
                "final_handler_id": "H002",
                "adjustment_type": "both",
                "adjustment_reason": "优化换机时间",
                "source_type": "success",
                "lot_data": {...}  // 完整的批次数据
            }
        ]
    }
    """
    try:
        data = request.get_json()
        
        if not data or 'session_id' not in data or 'adjustments' not in data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误，缺少必要字段'
            }), 400
        
        session_id = data['session_id']
        session_name = data.get('session_name', f'调整会话 {datetime.now().strftime("%Y-%m-%d %H:%M")}')
        adjustments = data['adjustments']
        
        if not adjustments:
            return jsonify({
                'success': False,
                'message': '调整数据为空'
            }), 400
        
        username = current_user.username if current_user.is_authenticated else 'anonymous'
        
        # 统计数据
        total_lots = len(adjustments)
        success_lots = len([adj for adj in adjustments if adj.get('source_type') == 'success'])
        failed_lots = len([adj for adj in adjustments if adj.get('source_type') == 'failed'])
        adjusted_lots = len([adj for adj in adjustments if adj.get('adjustment_type') != 'none'])
        
        # 1. 创建或更新会话记录
        session_sql = text("""
            INSERT INTO scheduling_sessions (
                session_id, session_name, description, base_data_source,
                total_lots, success_lots, failed_lots, adjusted_lots,
                status, created_by, created_at, updated_by, updated_at
            ) VALUES (
                :session_id, :session_name, :description, :base_data_source,
                :total_lots, :success_lots, :failed_lots, :adjusted_lots,
                'draft', :created_by, NOW(), :updated_by, NOW()
            )
            ON DUPLICATE KEY UPDATE
                session_name = VALUES(session_name),
                description = VALUES(description),
                total_lots = VALUES(total_lots),
                success_lots = VALUES(success_lots),
                failed_lots = VALUES(failed_lots),
                adjusted_lots = VALUES(adjusted_lots),
                updated_by = VALUES(updated_by),
                updated_at = NOW()
        """)
        
        db.session.execute(session_sql, {
            'session_id': session_id,
            'session_name': session_name,
            'description': f'包含 {total_lots} 个批次的调整结果',
            'base_data_source': 'lotprioritydone',
            'total_lots': total_lots,
            'success_lots': success_lots,
            'failed_lots': failed_lots,
            'adjusted_lots': adjusted_lots,
            'created_by': username,
            'updated_by': username
        })
        
        # 2. 删除该会话的现有调整结果（实现覆盖保存）
        delete_sql = text("DELETE FROM final_scheduling_result WHERE session_id = :session_id")
        delete_result = db.session.execute(delete_sql, {'session_id': session_id})
        deleted_count = delete_result.rowcount
        
        logger.info(f"🗑️ 删除会话 {session_id} 的现有记录: {deleted_count} 条")
        
        # 3. 插入新的调整结果
        insert_sql = text("""
            INSERT INTO final_scheduling_result (
                session_id, session_name, lot_id, lot_type, good_qty, prod_id, device, chip_id, 
                pkg_pn, po_id, stage, step, wip_state, proc_state, hold_state, flow_id, flow_ver,
                release_time, fac_id, create_time, final_priority, final_handler_id,
                source_type, original_priority, original_handler_id, failure_reason,
                adjustment_type, adjustment_reason, comprehensive_score, processing_time,
                changeover_time, match_type, algorithm_version, status, adjusted_by,
                adjusted_at, created_at, updated_at
            ) VALUES (
                :session_id, :session_name, :lot_id, :lot_type, :good_qty, :prod_id, :device, :chip_id,
                :pkg_pn, :po_id, :stage, :step, :wip_state, :proc_state, :hold_state, :flow_id, :flow_ver,
                :release_time, :fac_id, :create_time, :final_priority, :final_handler_id,
                :source_type, :original_priority, :original_handler_id, :failure_reason,
                :adjustment_type, :adjustment_reason, :comprehensive_score, :processing_time,
                :changeover_time, :match_type, :algorithm_version, :status, :adjusted_by,
                :adjusted_at, :created_at, :updated_at
            )
        """)
        
        saved_count = 0
        for adjustment in adjustments:
            lot_data = adjustment.get('lot_data', {})
            
            # 准备插入数据
            insert_data = {
                'session_id': session_id,
                'session_name': session_name,
                'lot_id': adjustment['lot_id'],
                'lot_type': lot_data.get('LOT_TYPE'),
                'good_qty': lot_data.get('GOOD_QTY', 0),
                'prod_id': lot_data.get('PROD_ID'),
                'device': lot_data.get('DEVICE'),
                'chip_id': lot_data.get('CHIP_ID'),
                'pkg_pn': lot_data.get('PKG_PN'),
                'po_id': lot_data.get('PO_ID'),
                'stage': lot_data.get('STAGE'),
                'step': lot_data.get('STEP', ''),
                'wip_state': lot_data.get('WIP_STATE', 'SCHEDULED'),
                'proc_state': lot_data.get('PROC_STATE', 'READY'),
                'hold_state': lot_data.get('HOLD_STATE'),
                'flow_id': lot_data.get('FLOW_ID'),
                'flow_ver': lot_data.get('FLOW_VER'),
                'release_time': parse_datetime(lot_data.get('RELEASE_TIME')),
                'fac_id': lot_data.get('FAC_ID'),
                'create_time': parse_datetime(lot_data.get('CREATE_TIME')),
                'final_priority': adjustment['final_priority'],
                'final_handler_id': adjustment['final_handler_id'],
                'source_type': adjustment.get('source_type', 'success'),
                'original_priority': adjustment.get('original_priority'),
                'original_handler_id': adjustment.get('original_handler_id'),
                'failure_reason': adjustment.get('failure_reason'),
                'adjustment_type': adjustment.get('adjustment_type', 'none'),
                'adjustment_reason': adjustment.get('adjustment_reason'),
                'comprehensive_score': lot_data.get('comprehensive_score'),
                'processing_time': lot_data.get('processing_time'),
                'changeover_time': lot_data.get('changeover_time'),
                'match_type': lot_data.get('match_type'),
                'algorithm_version': lot_data.get('algorithm_version'),
                'status': 'draft',
                'adjusted_by': username,
                'adjusted_at': datetime.now(),
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            }
            
            db.session.execute(insert_sql, insert_data)
            saved_count += 1
            
        # 提交事务
        db.session.commit()
        logger.info(f"✅ 保存调整结果成功: {saved_count}/{total_lots} 条记录")
        
        return jsonify({
            'success': True,
            'message': f'调整结果保存成功，共保存 {saved_count} 条记录',
            'data': {
                'session_id': session_id,
                'session_name': session_name,
                'saved_count': saved_count,
                'total_lots': total_lots,
                'success_lots': success_lots,
                'failed_lots': failed_lots,
                'adjusted_lots': adjusted_lots
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 保存调整结果失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'保存调整结果失败: {str(e)}'
        }), 500

@final_scheduling_api.route('/api/v2/production/final-scheduling/data', methods=['GET'])
@login_required
def get_adjustment_results():
    """
    获取调整结果数据
    
    参数：
    - session_id: 会话ID（可选，默认获取最新会话）
    - page: 页码（默认1）
    - size: 每页大小（默认50）
    - handler_id: 分选机ID过滤（可选）
    - source_type: 数据来源类型过滤（可选）
    - search: 搜索关键词（可选，支持内部工单号、产品名称等）
    """
    try:
        session_id = request.args.get('session_id')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        handler_id = request.args.get('handler_id')
        source_type = request.args.get('source_type')
        search = request.args.get('search', '').strip()
        
        offset = (page - 1) * size
        
        # 构建查询条件
        where_conditions = ["fsr.is_active = TRUE"]
        params = {}
        
        if session_id:
            where_conditions.append("fsr.session_id = :session_id")
            params['session_id'] = session_id
        else:
            # 获取最新会话的数据
            where_conditions.append("""
                fsr.session_id = (
                    SELECT session_id FROM final_scheduling_result 
                    WHERE is_active = TRUE
                    ORDER BY adjusted_at DESC 
                    LIMIT 1
                )
            """)
        
        if handler_id:
            where_conditions.append("fsr.final_handler_id = :handler_id")
            params['handler_id'] = handler_id
            
        if source_type:
            where_conditions.append("fsr.source_type = :source_type")
            params['source_type'] = source_type
        
        # 搜索条件
        if search:
            search_conditions = [
                "fsr.lot_id LIKE :search",
                "fsr.device LIKE :search",
                "fsr.prod_id LIKE :search",
                "fsr.chip_id LIKE :search",
                "fsr.pkg_pn LIKE :search",
                "fsr.final_handler_id LIKE :search",
                "fsr.match_type LIKE :search"
            ]
            where_conditions.append(f"({' OR '.join(search_conditions)})")
            params['search'] = f"%{search}%"
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询数据
        query_sql = text(f"""
            SELECT 
                fsr.*,
                ss.session_name as session_display_name,
                ss.description as session_description,
                ss.created_by as session_creator,
                ss.status as session_status
            FROM final_scheduling_result fsr
            LEFT JOIN scheduling_sessions ss ON fsr.session_id = ss.session_id
            WHERE {where_clause}
            ORDER BY fsr.final_priority ASC, fsr.final_handler_id ASC
            LIMIT :size OFFSET :offset
        """)
        
        params.update({'size': size, 'offset': offset})
        result = db.session.execute(query_sql, params)
        
        # 查询总数
        count_sql = text(f"""
            SELECT COUNT(*) as total
            FROM final_scheduling_result fsr
            LEFT JOIN scheduling_sessions ss ON fsr.session_id = ss.session_id
            WHERE {where_clause}
        """)
        
        count_params = {k: v for k, v in params.items() if k not in ['size', 'offset']}
        count_result = db.session.execute(count_sql, count_params)
        total = count_result.fetchone()[0]
        
        # 转换数据格式
        data = []
        for row in result:
            data.append({
                'ID': row.id,
                'SESSION_ID': row.session_id,
                'SESSION_NAME': row.session_display_name or row.session_name,
                'LOT_ID': row.lot_id,
                'LOT_TYPE': row.lot_type,
                'GOOD_QTY': row.good_qty,
                'PROD_ID': row.prod_id,
                'DEVICE': row.device,
                'CHIP_ID': row.chip_id,
                'PKG_PN': row.pkg_pn,
                'PO_ID': row.po_id,
                'STAGE': row.stage,
                'WIP_STATE': row.wip_state,
                'PROC_STATE': row.proc_state,
                'HOLD_STATE': row.hold_state,
                'FLOW_ID': row.flow_id,
                'FLOW_VER': row.flow_ver,
                'RELEASE_TIME': row.release_time.isoformat() if row.release_time else None,
                'FAC_ID': row.fac_id,
                'CREATE_TIME': row.create_time.isoformat() if row.create_time else None,
                'PRIORITY': row.final_priority,
                'HANDLER_ID': row.final_handler_id,
                'FINAL_PRIORITY': row.final_priority,
                'FINAL_HANDLER_ID': row.final_handler_id,
                'SOURCE_TYPE': row.source_type,
                'ORIGINAL_PRIORITY': row.original_priority,
                'ORIGINAL_HANDLER_ID': row.original_handler_id,
                'FAILURE_REASON': row.failure_reason,
                'ADJUSTMENT_TYPE': row.adjustment_type,
                'ADJUSTMENT_REASON': row.adjustment_reason,
                'COMPREHENSIVE_SCORE': float(row.comprehensive_score) if row.comprehensive_score else None,
                'PROCESSING_TIME': float(row.processing_time) if row.processing_time else None,
                'CHANGEOVER_TIME': float(row.changeover_time) if row.changeover_time else None,
                'MATCH_TYPE': row.match_type,
                'ALGORITHM_VERSION': row.algorithm_version,
                'STATUS': row.status,
                'ADJUSTED_BY': row.adjusted_by,
                'ADJUSTED_AT': row.adjusted_at.isoformat() if row.adjusted_at else None,
                'PUBLISHED_BY': row.published_by,
                'PUBLISHED_AT': row.published_at.isoformat() if row.published_at else None,
                'CREATED_AT': row.created_at.isoformat() if row.created_at else None,
                'UPDATED_AT': row.updated_at.isoformat() if row.updated_at else None
            })
        
        logger.info(f"📊 查询调整结果: 返回 {len(data)} 条记录，总计 {total} 条记录")
        
        return jsonify({
            'success': True,
            'data': data,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'pages': (total + size - 1) // size
            },
            'session_info': {
                'session_id': session_id,
                'session_name': data[0]['SESSION_NAME'] if data else None,
                'session_status': data[0]['STATUS'] if data else None
            },
            'data_source': 'final_scheduling_result',
            'message': f'查询成功，共 {total} 条记录'
        })
        
    except Exception as e:
        logger.error(f"❌ 查询调整结果失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询调整结果失败: {str(e)}'
        }), 500

@final_scheduling_api.route('/api/v2/production/final-scheduling/publish', methods=['POST'])
@login_required
def publish_adjustment_results():
    """
    发布调整结果到生产表（lotprioritydone）
    
    请求格式：
    {
        "session_id": "adjust_1752556996616",
        "publish_mode": "replace"  // replace=替换全部, append=追加
    }
    """
    try:
        data = request.get_json()
        
        if not data or 'session_id' not in data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误，缺少session_id'
            }), 400
        
        session_id = data['session_id']
        publish_mode = data.get('publish_mode', 'replace')
        username = current_user.username if current_user.is_authenticated else 'anonymous'
        
        # 🔧 修复：替换存储过程调用为直接SQL查询，避免依赖缺失的存储过程
        try:
            # 1. 检查会话是否存在
            session_check_sql = text("SELECT COUNT(*) FROM scheduling_sessions WHERE session_id = :session_id")
            session_result = db.session.execute(session_check_sql, {'session_id': session_id})
            session_count = session_result.scalar()
            
            if session_count == 0:
                logger.error(f"❌ 会话不存在: {session_id}")
                return jsonify({
                    'success': False,
                    'message': f'会话 {session_id} 不存在，无法发布调整结果'
                }), 400
            
            # 2. 清空生产表 lotprioritydone
            clear_sql = text("DELETE FROM lotprioritydone")
            db.session.execute(clear_sql)
            logger.info(f"✅ 已清空现有排产数据，准备保存新数据")
            
            # 3. 插入调整结果到生产表
            insert_sql = text("""
                INSERT INTO lotprioritydone (
                    HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID,
                    PKG_PN, PO_ID, STAGE, STEP, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID,
                    FLOW_VER, RELEASE_TIME, FAC_ID, CREATE_TIME, PRIORITY,
                    comprehensive_score, processing_time, changeover_time, algorithm_version, match_type
                )
                SELECT 
                    final_handler_id, lot_id, lot_type, good_qty, prod_id, device, chip_id,
                    pkg_pn, po_id, stage, COALESCE(step, '') as step, wip_state, proc_state, hold_state, flow_id,
                    flow_ver, release_time, fac_id, create_time, final_priority,
                    comprehensive_score, processing_time, changeover_time, algorithm_version, match_type
                FROM final_scheduling_result
                WHERE session_id = :session_id 
                AND is_active = TRUE
                AND source_type != 'failed'
            """)
            
            insert_result = db.session.execute(insert_sql, {'session_id': session_id})
            affected_rows = insert_result.rowcount
            
            # 4. 更新会话状态
            update_session_sql = text("""
                UPDATE scheduling_sessions 
                SET status = 'published', published_by = :published_by, published_at = NOW()
                WHERE session_id = :session_id
            """)
            db.session.execute(update_session_sql, {
                'session_id': session_id,
                'published_by': username
            })
            
            # 5. 更新调整结果状态
            update_results_sql = text("""
                UPDATE final_scheduling_result 
                SET status = 'published', published_by = :published_by, published_at = NOW()
                WHERE session_id = :session_id AND is_active = TRUE
            """)
            db.session.execute(update_results_sql, {
                'session_id': session_id,
                'published_by': username
            })
            
            # 6. 提交事务
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ 发布调整结果失败: {e}")
            return jsonify({
                'success': False,
                'message': f'发布调整结果失败: {str(e)}'
            }), 500
            
        logger.info(f"✅ 发布调整结果成功: 会话 {session_id}，影响 {affected_rows} 条记录")
        
        # Excel自动保存（如果启用）
        try:
            from app.services.excel_auto_save_service import get_excel_auto_save_service
            excel_service = get_excel_auto_save_service()
            
            if excel_service.is_auto_save_enabled():
                # 获取刚刚发布的数据
                published_data_sql = text("""
                    SELECT * FROM lotprioritydone 
                    ORDER BY PRIORITY ASC, CREATE_TIME DESC
                """)
                published_result = db.session.execute(published_data_sql)
                published_data = [dict(row._mapping) for row in published_result.fetchall()]
                
                if published_data:
                    # 构建指标信息
                    metrics = {
                        'session_id': session_id,
                        'published_by': username,
                        'published_at': datetime.now().isoformat(),
                        'total_records': affected_rows,
                        'source': 'final_adjustment_publish'
                    }
                    
                    # 自动保存Excel
                    save_result = excel_service.auto_save_schedule_result(
                        schedule_data=published_data,
                        source='publish',
                        metrics=metrics
                    )
                    
                    if save_result.get('success'):
                        logger.info(f"✅ 发布调整结果已自动保存为Excel: {save_result.get('filename')} (共{save_result.get('records_count')}条记录)")
                    else:
                        logger.warning(f"⚠️ 发布调整结果Excel自动保存失败: {save_result.get('message')}")
                        
        except Exception as excel_error:
            logger.error(f"❌ 发布调整结果Excel自动保存异常: {excel_error}")
            # Excel保存失败不影响主要流程
        
        return jsonify({
            'success': True,
            'message': f'调整结果发布成功，共发布 {affected_rows} 条记录到生产表',
            'data': {
                'session_id': session_id,
                'published_by': username,
                'published_at': datetime.now().isoformat(),
                'affected_rows': affected_rows
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 发布调整结果失败: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'发布调整结果失败: {str(e)}'
        }), 500 

@final_scheduling_api.route('/api/v2/production/final-scheduling/sessions', methods=['GET'])
@login_required
def get_adjustment_sessions():
    """
    获取调整会话列表
    """
    try:
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 20))
        status = request.args.get('status')
        
        offset = (page - 1) * size
        
        # 构建查询条件
        where_conditions = []
        params = {}
        
        if status:
            where_conditions.append("status = :status")
            params['status'] = status
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        # 查询会话列表
        query_sql = text(f"""
            SELECT 
                session_id, session_name, description, base_data_source,
                total_lots, success_lots, failed_lots, adjusted_lots,
                status, created_by, created_at, updated_by, updated_at,
                published_by, published_at
            FROM scheduling_sessions
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT :size OFFSET :offset
        """)
        
        params.update({'size': size, 'offset': offset})
        result = db.session.execute(query_sql, params)
        
        # 查询总数
        count_sql = text(f"""
            SELECT COUNT(*) as total
            FROM scheduling_sessions
            WHERE {where_clause}
        """)
        
        count_params = {k: v for k, v in params.items() if k not in ['size', 'offset']}
        count_result = db.session.execute(count_sql, count_params)
        total = count_result.fetchone()[0]
        
        # 转换数据格式
        sessions = []
        for row in result:
            sessions.append({
                'session_id': row.session_id,
                'session_name': row.session_name,
                'description': row.description,
                'base_data_source': row.base_data_source,
                'total_lots': row.total_lots,
                'success_lots': row.success_lots,
                'failed_lots': row.failed_lots,
                'adjusted_lots': row.adjusted_lots,
                'status': row.status,
                'created_by': row.created_by,
                'created_at': row.created_at.isoformat() if row.created_at else None,
                'updated_by': row.updated_by,
                'updated_at': row.updated_at.isoformat() if row.updated_at else None,
                'published_by': row.published_by,
                'published_at': row.published_at.isoformat() if row.published_at else None
            })
        
        return jsonify({
            'success': True,
            'data': sessions,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'pages': (total + size - 1) // size
            },
            'message': f'查询成功，共 {total} 个会话'
        })
        
    except Exception as e:
        logger.error(f"❌ 查询会话列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询会话列表失败: {str(e)}'
        }), 500 

# 注册蓝图到应用
def register_final_scheduling_api(app):
    """注册最终排产调整结果API蓝图"""
    app.register_blueprint(final_scheduling_api) 