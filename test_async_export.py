#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段3.2 - 异步导出功能测试
测试大数据导出的进度跟踪和任务管理
"""

# 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 基础导入
import os
import logging
import time
import json
import requests
from datetime import datetime

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_async_export.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestAsyncExport')

def test_export_task_manager():
    """测试导出任务管理器"""
    try:
        # Flask应用创建
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入任务管理器
            from app.services.export_task_manager import export_task_manager
            
            logger.info("📤 测试导出任务管理器...")
            
            # 测试1: 创建导出任务
            export_config = {
                'mode': 'view',
                'export_type': 'filtered',
                'format': 'excel',
                'filters': {
                    'search': 'test',
                    'priority_min': 50
                },
                'user_id': 1
            }
            
            task_id = export_task_manager.create_export_task(export_config)
            logger.info(f"✅ 创建导出任务成功: {task_id}")
            
            # 测试2: 获取任务状态
            status = export_task_manager.get_task_status(task_id)
            logger.info(f"📊 任务状态: {status['status']} - {status['message']}")
            
            # 测试3: 等待任务完成（最多等待30秒）
            max_wait = 30
            wait_count = 0
            
            while wait_count < max_wait:
                status = export_task_manager.get_task_status(task_id)
                logger.info(f"⏱️ 等待中({wait_count}s): {status['status']} - 进度{status['progress']}%")
                
                if status['status'] in ['completed', 'failed']:
                    break
                
                time.sleep(1)
                wait_count += 1
            
            # 最终状态检查
            final_status = export_task_manager.get_task_status(task_id)
            if final_status['status'] == 'completed':
                logger.info(f"🎉 任务完成: {final_status['records_count']}条记录")
                logger.info(f"📁 文件路径: {final_status['file_path']}")
                
                # 检查文件是否存在
                if final_status['file_path'] and os.path.exists(final_status['file_path']):
                    file_size = os.path.getsize(final_status['file_path'])
                    logger.info(f"📊 文件大小: {file_size} 字节")
                else:
                    logger.warning("⚠️ 导出文件不存在")
                    
            else:
                logger.error(f"❌ 任务未完成: {final_status['status']}")
            
            # 测试4: 获取任务列表
            all_tasks = export_task_manager.get_all_tasks(limit=5)
            logger.info(f"📋 获取到 {len(all_tasks)} 个任务")
            
            for task in all_tasks:
                logger.info(f"   - {task['task_id'][:8]}: {task['status']} ({task['progress']}%)")
            
            logger.info("✅ 导出任务管理器测试完成")
            return True
            
    except Exception as e:
        logger.error(f"❌ 导出任务管理器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_async_api_endpoints():
    """测试异步导出API端点"""
    try:
        logger.info("🌐 测试异步导出API端点...")
        
        base_url = "http://localhost:5000"
        
        # 测试1: 创建异步导出任务
        create_payload = {
            "mode": "view",
            "export_type": "filtered", 
            "format": "excel",
            "search": "test",
            "priority_min": 30
        }
        
        try:
            response = requests.post(
                f"{base_url}/api/v2/production/done-lots/export-async",
                json=create_payload,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get('task_id')
                logger.info(f"✅ 创建异步导出任务成功: {task_id}")
                
                # 测试2: 获取任务状态
                status_response = requests.get(
                    f"{base_url}/api/v2/production/done-lots/export-status/{task_id}",
                    timeout=5
                )
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    logger.info(f"📊 任务状态查询成功: {status_data['data']['status']}")
                else:
                    logger.warning(f"⚠️ 任务状态查询失败: {status_response.status_code}")
                
                # 测试3: 监控任务进度（最多30秒）
                monitor_count = 0
                while monitor_count < 30:
                    status_response = requests.get(
                        f"{base_url}/api/v2/production/done-lots/export-status/{task_id}",
                        timeout=5
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        task_status = status_data['data']
                        
                        logger.info(f"⏱️ 监控进度({monitor_count}s): {task_status['status']} - {task_status['progress']}%")
                        
                        if task_status['status'] in ['completed', 'failed', 'cancelled']:
                            if task_status['status'] == 'completed':
                                logger.info(f"🎉 任务完成: {task_status.get('records_count', 0)}条记录")
                                
                                # 测试4: 下载文件
                                download_response = requests.get(
                                    f"{base_url}/api/v2/production/done-lots/export-download/{task_id}",
                                    timeout=30
                                )
                                
                                if download_response.status_code == 200:
                                    logger.info(f"📥 文件下载测试成功: {len(download_response.content)} 字节")
                                else:
                                    logger.warning(f"⚠️ 文件下载失败: {download_response.status_code}")
                            
                            break
                    
                    time.sleep(1)
                    monitor_count += 1
                
                # 测试5: 获取任务列表
                tasks_response = requests.get(
                    f"{base_url}/api/v2/production/done-lots/export-tasks?limit=5",
                    timeout=5
                )
                
                if tasks_response.status_code == 200:
                    tasks_data = tasks_response.json()
                    logger.info(f"📋 任务列表查询成功: {len(tasks_data['data'])}个任务")
                else:
                    logger.warning(f"⚠️ 任务列表查询失败: {tasks_response.status_code}")
                
                return True
                
            else:
                logger.warning(f"⚠️ 创建异步导出任务失败: {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            logger.warning("⚠️ API端点测试失败: Flask应用未运行")
            return False
        except Exception as e:
            logger.error(f"❌ API端点测试失败: {e}")
            return False
    
    except Exception as e:
        logger.error(f"❌ 异步导出API测试失败: {e}")
        return False

def test_large_data_export():
    """测试大数据量导出性能"""
    try:
        logger.info("🔥 测试大数据量导出性能...")
        
        # Flask应用创建
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            # 导入任务管理器
            from app.services.export_task_manager import export_task_manager
            
            # 创建大数据量导出任务
            large_export_config = {
                'mode': 'adjust',  # 调整模式包含更多数据
                'export_type': 'all',  # 全量数据
                'format': 'excel',
                'filters': {},  # 无筛选条件，全量导出
                'user_id': 1
            }
            
            start_time = time.time()
            task_id = export_task_manager.create_export_task(large_export_config)
            logger.info(f"🚀 创建大数据导出任务: {task_id}")
            
            # 监控性能指标
            last_progress = 0
            performance_data = []
            
            while True:
                current_time = time.time()
                elapsed = current_time - start_time
                
                status = export_task_manager.get_task_status(task_id)
                progress = status['progress']
                
                # 记录性能数据
                if progress > last_progress:
                    performance_data.append({
                        'elapsed': elapsed,
                        'progress': progress,
                        'message': status['message']
                    })
                    last_progress = progress
                
                logger.info(f"⚡ 性能监控({elapsed:.1f}s): {progress}% - {status['message']}")
                
                if status['status'] in ['completed', 'failed']:
                    break
                
                if elapsed > 120:  # 最多等待2分钟
                    logger.warning("⚠️ 超时，停止监控")
                    export_task_manager.cancel_task(task_id)
                    break
                
                time.sleep(2)
            
            # 性能分析
            total_time = time.time() - start_time
            final_status = export_task_manager.get_task_status(task_id)
            
            logger.info("📈 性能分析结果:")
            logger.info(f"   总耗时: {total_time:.1f}秒")
            logger.info(f"   最终状态: {final_status['status']}")
            logger.info(f"   记录数: {final_status.get('records_count', 0)}")
            
            if final_status.get('file_size'):
                throughput = final_status['file_size'] / total_time
                logger.info(f"   文件大小: {final_status['file_size']} 字节")
                logger.info(f"   处理吞吐量: {throughput:.1f} 字节/秒")
            
            # 处理速度分析
            if len(performance_data) >= 2:
                avg_speed = 100 / total_time  # 进度百分比每秒
                logger.info(f"   平均处理速度: {avg_speed:.2f}%/秒")
            
            return True
    
    except Exception as e:
        logger.error(f"❌ 大数据导出性能测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始阶段3.2异步导出功能测试...")
    
    success = True
    
    # 测试1: 导出任务管理器
    logger.info("=" * 60)
    logger.info("测试1: 导出任务管理器")
    if not test_export_task_manager():
        success = False
    
    # 测试2: 异步API端点
    logger.info("=" * 60) 
    logger.info("测试2: 异步导出API端点")
    if not test_async_api_endpoints():
        success = False
    
    # 测试3: 大数据导出性能
    logger.info("=" * 60)
    logger.info("测试3: 大数据导出性能")
    if not test_large_data_export():
        success = False
    
    # 总结
    logger.info("=" * 60)
    if success:
        logger.info("🎉 阶段3.2异步导出功能测试: 全部通过")
    else:
        logger.error("❌ 阶段3.2异步导出功能测试: 部分失败")
    
    return success

if __name__ == "__main__":
    success = main()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")