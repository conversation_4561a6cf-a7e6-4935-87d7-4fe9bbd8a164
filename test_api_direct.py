#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API端点，确定问题所在
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import requests
import json

def test_api_endpoints():
    """测试API端点是否正常工作"""
    base_url = "http://localhost:5000"
    
    print("🚀 开始测试API端点...")
    
    try:
        # 测试1: 基本的done-lots API
        print("1. 测试基本done-lots API...")
        response = requests.get(f"{base_url}/api/v2/production/done-lots?table=lotprioritydone&page=1&size=50", timeout=10)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API正常: {len(data.get('data', []))}条数据")
            print(f"总数: {data.get('pagination', {}).get('total', '未知')}")
        else:
            print(f"❌ API失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Flask应用，请确认应用正在运行在localhost:5000")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False
    
    try:
        # 测试2: 失败批次API
        print("\n2. 测试失败批次API...")
        response = requests.get(f"{base_url}/api/v2/production/get-failed-lots-from-logs?current_only=true&hours=24", timeout=5)
        print(f"失败批次API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 失败批次API正常")
        else:
            print(f"⚠️ 失败批次API失败: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ 失败批次API异常: {e}")
    
    try:
        # 测试3: 大数据量请求
        print("\n3. 测试大数据量请求...")
        response = requests.get(f"{base_url}/api/v2/production/done-lots?table=lotprioritydone&page=1&size=500", timeout=15)
        print(f"大数据量请求状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 大数据量请求正常: {len(data.get('data', []))}条数据")
            print(f"总数: {data.get('pagination', {}).get('total', '未知')}")
        else:
            print(f"❌ 大数据量请求失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 大数据量请求异常: {e}")
        
    return True

if __name__ == "__main__":
    test_api_endpoints()