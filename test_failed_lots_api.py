#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试失败批次API数据更新问题
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import requests
import time

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_failed_lots_api.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestFailedLotsAPI')

def test_failed_lots_api():
    """测试失败批次API数据更新问题"""
    try:
        base_url = "http://localhost:5000"
        
        print("🔍 开始测试失败批次API数据更新问题...")
        
        # 测试1: 当前失败批次
        print("\n1. 测试当前失败批次API...")
        current_url = f"{base_url}/api/v2/production/get-failed-lots-from-logs?current_only=true&hours=24"
        
        # 添加缓存破坏参数
        cache_bust = int(time.time())
        current_url_with_cache_bust = f"{current_url}&_cache_bust={cache_bust}&_force=true"
        
        print(f"请求URL: {current_url_with_cache_bust}")
        
        response = requests.get(current_url_with_cache_bust, timeout=10)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API响应成功: {data.get('success', False)}")
            
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                total_failed = data.get('data', {}).get('summary', {}).get('total_failed', 0)
                
                print(f"✅ 当前失败批次数据: {len(failed_lots)}条")
                print(f"📊 统计信息总数: {total_failed}")
                
                # 显示数据源信息
                debug_info = data.get('debug_info', {})
                if debug_info:
                    print(f"🔍 数据源: {debug_info.get('data_source', 'unknown')}")
                    print(f"📋 表名: {debug_info.get('table_name', 'unknown')}")
                    print(f"📈 记录数: {debug_info.get('total_records', 'unknown')}")
                    print(f"⏰ 查询时间: {debug_info.get('query_time', 'unknown')}")
                
                # 显示最近几条记录的时间戳
                if failed_lots:
                    print(f"\n最近5条失败记录:")
                    for i, lot in enumerate(failed_lots[:5]):
                        timestamp = lot.get('timestamp', 'unknown')
                        lot_id = lot.get('LOT_ID', lot.get('lot_id', 'unknown'))
                        print(f"  {i+1}. {lot_id} - {timestamp}")
                
            else:
                print(f"❌ API返回失败: {data.get('message', 'unknown error')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"错误内容: {response.text[:200]}...")
            
        # 测试2: 历史失败批次
        print("\n2. 测试历史失败批次API...")
        history_url = f"{base_url}/api/v2/production/get-failed-lots-from-logs?current_only=false"
        history_url_with_cache_bust = f"{history_url}&_cache_bust={cache_bust}&_force=true"
        
        print(f"请求URL: {history_url_with_cache_bust}")
        
        response = requests.get(history_url_with_cache_bust, timeout=15)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                total_failed = data.get('data', {}).get('summary', {}).get('total_failed', 0)
                
                print(f"✅ 历史失败批次数据: {len(failed_lots)}条")
                print(f"📊 统计信息总数: {total_failed}")
                
                # 比较当前和历史的数据量
                if len(failed_lots) > 0:
                    print(f"\n💡 历史记录比当前记录多 {len(failed_lots)} 条")
                    
        # 测试3: 直接测试数据库
        print("\n3. 测试直接数据库查询...")
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.app_context():
                from app.utils.db_connection_pool import get_db_connection_context
                
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    
                    # 查询表记录总数
                    cursor.execute("SELECT COUNT(*) FROM scheduling_failed_lots")
                    total_count = cursor.fetchone()
                    if isinstance(total_count, dict):
                        total_count = list(total_count.values())[0]
                    else:
                        total_count = total_count[0]
                    
                    print(f"📋 数据库中失败批次总数: {total_count}")
                    
                    # 查询最近24小时的记录
                    cursor.execute("""
                        SELECT COUNT(*) 
                        FROM scheduling_failed_lots 
                        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    """)
                    recent_count = cursor.fetchone()
                    if isinstance(recent_count, dict):
                        recent_count = list(recent_count.values())[0]
                    else:
                        recent_count = recent_count[0]
                    
                    print(f"📋 最近24小时失败批次: {recent_count}")
                    
                    # 查询最新的几条记录时间
                    cursor.execute("""
                        SELECT lot_id, timestamp 
                        FROM scheduling_failed_lots 
                        ORDER BY timestamp DESC 
                        LIMIT 5
                    """)
                    recent_records = cursor.fetchall()
                    print(f"\n最新5条数据库记录:")
                    for record in recent_records:
                        if isinstance(record, dict):
                            lot_id = record.get('lot_id', 'unknown')
                            timestamp = record.get('timestamp', 'unknown')
                        else:
                            lot_id = record[0]
                            timestamp = record[1]
                        print(f"  • {lot_id} - {timestamp}")
                    
        except Exception as db_error:
            print(f"❌ 数据库测试失败: {db_error}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_failed_lots_api()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")

if __name__ == "__main__":
    main()