#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
温度感知STAGE匹配集成测试
验证集成到real_scheduling_service.py中的温度匹配功能
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_integrated_temperature_matching.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestIntegratedTemperatureMatching')

def test_integrated_temperature_matching():
    """测试集成的温度感知STAGE匹配功能"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 6. 导入增强后的调度服务
            from app.services.real_scheduling_service import RealSchedulingService
            
            # 创建调度服务实例
            scheduler = RealSchedulingService()
            logger.info("✅ 调度服务实例创建成功")
            
            # 7. 测试温度感知匹配功能
            test_cases = [
                # (批次STAGE, 设备STAGE, 设备温度范围, 设备类型, 预期结果, 描述)
                ("Cold", "ROOM", "-50-150", "PnP", True, "全温PnP机台可以做低温测试"),
                ("Cold", "ROOM", "25", "PnP", False, "常温PnP机台无法做低温测试"),
                ("Hot", "ROOM", "25-150", "PnP", True, "中温PnP机台可以做高温测试"),
                ("Hot", "ROOM", "25", "PnP", False, "常温PnP机台无法做高温测试"),
                
                # ROOM-TTR的EQP_CLASS约束测试
                ("ROOM-TTR", "ROOM", "25", "Turret", True, "Turret机台可以做ROOM-TTR测试"),
                ("ROOM-TTR", "ROOM", "25", "Gravity", True, "Gravity机台可以做ROOM-TTR测试"),  
                ("ROOM-TTR", "ROOM", "25", "PnP", False, "PnP机台不能做ROOM-TTR测试"),
                ("ROOM-TTR", "ROOM", "-50-150", "PnP", False, "即使全温PnP机台也不能做ROOM-TTR测试"),
                
                ("BAKING", "ROOM", "25-150", "PnP", True, "中温PnP机台可以做烘烤测试"),
                ("BAKING", "ROOM", "25", "PnP", False, "常温PnP机台无法做烘烤测试"),
            ]
            
            logger.info("🌡️ 开始测试集成的温度感知STAGE匹配功能")
            logger.info("=" * 80)
            
            success_count = 0
            total_count = len(test_cases)
            
            for i, (lot_stage, eqp_stage, temp_range, eqp_class, expected, description) in enumerate(test_cases, 1):
                # 构造设备信息
                equipment = {
                    'STAGE': eqp_stage,
                    'TEMPERATURE_RANGE': temp_range,
                    'EQP_CLASS': eqp_class,
                    'HANDLER_ID': f'TEST-{eqp_class}-{i:02d}'
                }
                
                # 空的预加载数据
                preloaded_data = {}
                
                # 调用集成后的_smart_stage_match方法
                result = scheduler._smart_stage_match(lot_stage, eqp_stage, preloaded_data, equipment)
                
                # 检查结果
                status = "PASS" if result == expected else "FAIL"
                status_emoji = "✅" if result == expected else "❌"
                
                logger.info(f"{i:2d}. {status_emoji} {status} | {temp_range:8s} + {lot_stage:10s} ({eqp_class:7s}) -> {result} | {description}")
                
                if result == expected:
                    success_count += 1
                else:
                    logger.error(f"    Expected: {expected}, Actual: {result}")
            
            logger.info("=" * 80)
            logger.info(f"🎯 测试结果总结: {success_count}/{total_count} 通过 ({100*success_count/total_count:.1f}%)")
            
            if success_count == total_count:
                logger.info("🎉 所有测试用例通过！温度感知STAGE匹配集成成功")
                return True
            else:
                logger.error(f"❌ {total_count - success_count} 个测试用例失败")
                return False
            
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_scheduling_with_temperature_awareness():
    """测试带温度感知的完整调度流程"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("🚀 开始测试带温度感知的完整调度流程")
            
            # 导入调度服务
            from app.services.real_scheduling_service import RealSchedulingService
            scheduler = RealSchedulingService()
            
            # 模拟一些测试数据
            logger.info("📊 模拟测试数据...")
            
            # 检查调度服务的温度匹配初始化
            if hasattr(scheduler, 'stage_temp_requirements'):
                logger.info(f"✅ 温度需求配置已加载，包含{len(scheduler.stage_temp_requirements)}个STAGE定义")
            else:
                logger.error("❌ 温度需求配置未正确初始化")
                return False
            
            if hasattr(scheduler, 'eqp_class_constraints'):
                logger.info(f"✅ EQP_CLASS约束配置已加载，包含{len(scheduler.eqp_class_constraints)}个约束")
            else:
                logger.error("❌ EQP_CLASS约束配置未正确初始化")
                return False
            
            logger.info("🎉 温度感知调度功能集成验证成功")
            return True
            
    except Exception as e:
        logger.error(f"❌ 调度流程测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    logger.info("🌡️ 开始集成温度感知STAGE匹配测试")
    logger.info("=" * 80)
    
    # 测试1: 温度感知匹配功能
    test1_success = test_integrated_temperature_matching()
    
    logger.info("")
    
    # 测试2: 完整调度流程验证
    test2_success = test_scheduling_with_temperature_awareness()
    
    logger.info("=" * 80)
    
    overall_success = test1_success and test2_success
    if overall_success:
        logger.info("🎉 集成测试: 全部通过")
        print("🎉 测试: 通过")
    else:
        logger.error("❌ 集成测试: 部分失败")
        print("❌ 测试: 失败")
    
    return overall_success

if __name__ == "__main__":
    main()