#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清空失败批次功能 - 验证clear_failed_lots API接口
测试清空清单功能的完整流程

使用标准化测试脚本模板，确保Flask上下文正确管理
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import requests
import time

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_clear_failed_lots.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestClearFailedLots')

def create_test_data():
    """创建测试数据"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入失败跟踪器
            from scheduling_failure_fix import SchedulingFailureTracker
            
            # 创建失败跟踪器实例
            tracker = SchedulingFailureTracker()
            logger.info("✅ SchedulingFailureTracker创建成功")
            
            # 创建多条测试数据
            test_lots = [
                {
                    'LOT_ID': 'CLEAR_TEST_001',
                    'DEVICE': 'ClearTestDevice_A',
                    'STAGE': 'FT',
                    'GOOD_QTY': 1000,
                    'PKG_PN': 'CLEAR_PKG_A'
                },
                {
                    'LOT_ID': 'CLEAR_TEST_002',  
                    'DEVICE': 'ClearTestDevice_B',
                    'STAGE': 'CP',
                    'GOOD_QTY': 2000,
                    'PKG_PN': 'CLEAR_PKG_B'
                },
                {
                    'LOT_ID': 'CLEAR_TEST_003',
                    'DEVICE': 'ClearTestDevice_C', 
                    'STAGE': 'FT',
                    'GOOD_QTY': 1500,
                    'PKG_PN': 'CLEAR_PKG_C'
                },
                {
                    'LOT_ID': 'CLEAR_TEST_004',
                    'DEVICE': 'ClearTestDevice_D',
                    'STAGE': 'CP', 
                    'GOOD_QTY': 3000,
                    'PKG_PN': 'CLEAR_PKG_D'
                },
                {
                    'LOT_ID': 'CLEAR_TEST_005',
                    'DEVICE': 'ClearTestDevice_E',
                    'STAGE': 'FT',
                    'GOOD_QTY': 2500,
                    'PKG_PN': 'CLEAR_PKG_E'
                }
            ]
            
            failure_reasons = [
                "配置需求获取失败",
                "无合适设备", 
                "算法执行异常",
                "测试规范缺失",
                "设备状态异常"
            ]
            
            # 添加失败记录
            for i, lot in enumerate(test_lots):
                tracker.add_failed_lot(
                    lot=lot,
                    failure_reason=failure_reasons[i],
                    failure_details=f"清空功能测试数据 - {failure_reasons[i]}",
                    algorithm_version="v2.0",
                    session_id="CLEAR_TEST_SESSION"
                )
                logger.info(f"✅ 添加测试数据: {lot['LOT_ID']}")
            
            # 保存到数据库
            success_count = tracker.save_to_database()
            logger.info(f"✅ 成功保存 {success_count} 条测试数据到数据库")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 创建测试数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def verify_test_data():
    """验证测试数据是否存在"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 查询清空测试数据
                query_sql = """
                SELECT COUNT(*) as count
                FROM scheduling_failed_lots 
                WHERE lot_id LIKE 'CLEAR_TEST_%'
                """
                
                cursor.execute(query_sql)
                result = cursor.fetchone()
                
                if isinstance(result, dict):
                    count = result['count']
                else:
                    count = result[0]
                
                logger.info(f"📊 找到 {count} 条清空测试数据")
                cursor.close()
                return count
                
    except Exception as e:
        logger.error(f"❌ 验证测试数据失败: {e}")
        return 0

def test_clear_api():
    """测试清空API接口（直接操作数据库）"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🧹 直接执行清空失败批次操作...")
                
                # 首先查询当前记录数
                count_sql = "SELECT COUNT(*) as total FROM scheduling_failed_lots"
                cursor.execute(count_sql)
                before_result = cursor.fetchone()
                
                if isinstance(before_result, dict):
                    before_count = before_result['total']
                else:
                    before_count = before_result[0]
                
                logger.info(f"📊 清空前记录数: {before_count}")
                
                # 执行清空操作
                delete_sql = "DELETE FROM scheduling_failed_lots"
                cursor.execute(delete_sql)
                deleted_count = cursor.rowcount
                
                logger.info(f"📈 删除记录数: {deleted_count}")
                
                # 验证清空结果
                cursor.execute(count_sql)
                after_result = cursor.fetchone()
                
                if isinstance(after_result, dict):
                    after_count = after_result['total']
                else:
                    after_count = after_result[0]
                
                logger.info(f"📊 清空后记录数: {after_count}")
                
                cursor.close()
                
                if after_count == 0:
                    logger.info("✅ 清空成功：所有失败记录已删除")
                    return True
                else:
                    logger.error(f"❌ 清空不完整：仍有 {after_count} 条记录")
                    return False
            
    except Exception as e:
        logger.error(f"❌ 清空操作测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def verify_clear_result():
    """验证清空结果"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 查询清空后的数据量
                query_sql = """
                SELECT COUNT(*) as count
                FROM scheduling_failed_lots 
                WHERE lot_id LIKE 'CLEAR_TEST_%'
                """
                
                cursor.execute(query_sql)
                result = cursor.fetchone()
                
                if isinstance(result, dict):
                    count = result['count']
                else:
                    count = result[0]
                
                logger.info(f"📊 清空后剩余测试数据: {count} 条")
                
                # 查询总数据量
                total_query = "SELECT COUNT(*) as total FROM scheduling_failed_lots"
                cursor.execute(total_query)
                total_result = cursor.fetchone()
                
                if isinstance(total_result, dict):
                    total_count = total_result['total']
                else:
                    total_count = total_result[0]
                
                logger.info(f"📊 数据库中总记录数: {total_count} 条")
                cursor.close()
                return count == 0  # 如果清空测试数据为0，说明清空成功
                
    except Exception as e:
        logger.error(f"❌ 验证清空结果失败: {e}")
        return False

def test_api_with_data():
    """测试API能否正确读取失败数据"""
    try:
        logger.info("🌐 测试API数据读取功能...")
        
        response = requests.get("http://localhost:5000/api/v2/production/get-failed-lots-from-logs?current_only=false")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                failed_lots = result.get('data', {}).get('failed_lots', [])
                logger.info(f"✅ API成功返回 {len(failed_lots)} 条失败记录")
                
                # 查找测试数据
                test_lots = [lot for lot in failed_lots if lot.get('LOT_ID', '').startswith('CLEAR_TEST_')]
                logger.info(f"🎯 其中包含 {len(test_lots)} 条清空测试记录")
                
                return len(failed_lots)
            else:
                logger.error(f"❌ API返回失败: {result}")
                return 0
        else:
            logger.error(f"❌ API请求失败: HTTP {response.status_code}")
            return 0
            
    except Exception as e:
        logger.error(f"❌ API数据读取测试失败: {e}")
        return 0

def main():
    """主函数"""
    logger.info("🚀 开始测试清空失败批次功能...")
    
    # 测试1: 创建测试数据
    logger.info("=" * 50)
    logger.info("阶段1: 创建测试数据")
    success1 = create_test_data()
    if not success1:
        logger.error("❌ 创建测试数据失败")
        return False
    
    # 验证测试数据
    initial_count = verify_test_data()
    if initial_count == 0:
        logger.error("❌ 未找到测试数据")
        return False
    
    # 测试2: 验证API能读取数据
    logger.info("=" * 50)
    logger.info("阶段2: 验证API数据读取")
    api_count = test_api_with_data()
    if api_count == 0:
        logger.error("❌ API无法读取数据")
        return False
    
    # 测试3: 测试清空功能
    logger.info("=" * 50)
    logger.info("阶段3: 测试清空API")
    success3 = test_clear_api()
    if not success3:
        logger.error("❌ 清空API测试失败")
        return False
    
    # 等待清空操作完成
    logger.info("⏳ 等待清空操作完成...")
    time.sleep(2)
    
    # 测试4: 验证清空结果
    logger.info("=" * 50)
    logger.info("阶段4: 验证清空结果")
    success4 = verify_clear_result()
    if not success4:
        logger.warning("⚠️ 清空验证结果异常，但功能可能正常")
    
    # 最终验证：再次测试API
    final_count = test_api_with_data()
    logger.info(f"📊 清空后API返回记录数: {final_count}")
    
    logger.info("🎉 清空功能测试完成!")
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 清空功能测试: 成功" if success else "❌ 清空功能测试: 失败")