<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已排产批次 - 统一组件集成测试</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* 基础样式 */
        body {
            background-color: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: none;
        }

        .card-header {
            background: linear-gradient(135deg, #b72424 0%, #b72424 100%);
            color: white;
            font-weight: bold;
        }

        /* 按钮样式 */
        .btn-console {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            line-height: 1.5;
            border-radius: 0.375rem;
            white-space: nowrap;
        }

        /* 统计卡片样式 */
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 6px;
            padding: 0.6rem;
            text-align: center;
            border: 1px solid #dee2e6;
            margin-bottom: 0.5rem;
        }

        .stats-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.15rem;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.8rem;
            margin: 0;
        }

        /* 模式切换样式 */
        .mode-toggle-section {
            border-bottom: 2px solid #f8f9fa;
            padding-bottom: 1rem;
            margin-bottom: 1rem;
        }

        .btn-check:checked + .btn-outline-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .btn-check:checked + .btn-outline-danger {
            background-color: #b72424;
            border-color: #b72424;
            color: white;
        }

        .btn-check:checked + .btn-outline-success {
            background-color: #198754;
            border-color: #198754;
            color: white;
        }

        /* 表格容器样式 */
        .table-container {
            min-height: 400px;
            position: relative;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .table-responsive-custom {
            max-height: 600px;
            overflow-y: auto;
        }

        .table th {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            z-index: 5;
        }

        /* 状态样式 */
        .status-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.7rem;
            font-weight: 500;
            color: white;
        }

        .status-success { background-color: #28a745; }
        .status-warning { background-color: #ffc107; color: #212529; }
        .status-danger { background-color: #dc3545; }
        .status-info { background-color: #17a2b8; }

        /* 优先级样式 */
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #ffc107; font-weight: bold; }
        .priority-low { color: #28a745; font-weight: normal; }

        /* 日志样式 */
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 0.25rem;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
        }

        .log-info { color: #17a2b8; }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <!-- 页面头部 -->
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tasks me-2"></i>已排产批次 - 统一组件集成测试
                            </h5>
                            <div>
                                <button type="button" class="btn btn-outline-light btn-console me-2" onclick="refreshData()">
                                    <i class="fas fa-sync-alt me-1"></i>刷新
                                </button>
                                <button type="button" class="btn btn-outline-light btn-console" onclick="exportData()">
                                    <i class="fas fa-file-excel me-1"></i>导出数据
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- 测试状态指示 -->
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>测试状态：</strong>
                            <span id="testStatus">组件初始化中...</span>
                        </div>
                        
                        <!-- 模式切换区域 -->
                        <div class="mode-toggle-section">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="viewMode" id="viewModeBtn" checked>
                                    <label class="btn btn-outline-primary" for="viewModeBtn">
                                        <i class="fas fa-table me-1"></i>查看模式
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="viewMode" id="adjustModeBtn">
                                    <label class="btn btn-outline-danger" for="adjustModeBtn">
                                        <i class="fas fa-tools me-1"></i>调整模式
                                    </label>
                                    
                                    <input type="radio" class="btn-check" name="viewMode" id="finalResultModeBtn">
                                    <label class="btn btn-outline-success" for="finalResultModeBtn">
                                        <i class="fas fa-check-circle me-1"></i>最终结果
                                    </label>
                                </div>
                                
                                <div class="mode-actions">
                                    <button type="button" class="btn btn-success btn-console me-2" id="saveAdjustmentsBtn" style="display: none;">
                                        <i class="fas fa-save me-1"></i>保存调整
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-console" id="cancelAdjustmentsBtn" style="display: none;">
                                        <i class="fas fa-times me-1"></i>取消调整
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div class="row mb-3" id="statisticsSection">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-number text-primary" id="totalRecords">-</div>
                                    <p class="stats-label">总记录数</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-number text-success" id="totalQuantity">-</div>
                                    <p class="stats-label">总数量</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-number text-info" id="handlerCount">-</div>
                                    <p class="stats-label">分选机数</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="stats-number text-warning" id="avgScore">-</div>
                                    <p class="stats-label">平均评分</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 筛选区域 -->
                        <div id="filterContainer" class="mb-3"></div>
                        
                        <!-- 数据展示区域 -->
                        <div class="table-container" id="dataContainer">
                            <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                            
                            <!-- 测试数据表格 -->
                            <div id="tableContainer" style="display: block;">
                                <div class="table-responsive-custom">
                                    <table class="table table-hover table-sm" id="dataTable">
                                        <thead>
                                            <tr>
                                                <th width="50">
                                                    <input type="checkbox" id="selectAll">
                                                </th>
                                                <th>优先级</th>
                                                <th>内部工单号</th>
                                                <th>产品名称</th>
                                                <th>分选机</th>
                                                <th>数量</th>
                                                <th>评分</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th width="120">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="tableBody">
                                            <tr>
                                                <td colspan="10" class="text-center text-muted">等待数据加载...</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 分页区域 -->
                        <div id="paginationContainer" class="mt-3"></div>
                        
                        <!-- 测试日志 -->
                        <div class="log-container">
                            <h6><i class="fas fa-terminal me-2"></i>测试日志</h6>
                            <div id="logOutput">
                                <!-- 日志将通过JavaScript动态添加 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 引入新开发的组件 -->
    <script src="app/static/js/done_lots_data_manager.js"></script>
    <script src="app/static/js/done_lots_filter_ui.js"></script>
    <script src="app/static/js/done_lots_pagination.js"></script>

    <script>
        // 全局变量
        let dataManager;
        let filterUI;
        let pagination;
        let testStatus = '初始化中...';

        // 日志记录
        function addLog(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // 更新测试状态
        function updateTestStatus(status, type = 'info') {
            testStatus = status;
            const statusElement = document.getElementById('testStatus');
            statusElement.textContent = status;
            statusElement.className = `text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'}`;
            addLog(status, type);
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 开始初始化已排产批次统一组件测试页面...');
            
            try {
                // 检查组件是否加载
                if (typeof DoneLotsDataManager === 'undefined') {
                    throw new Error('DoneLotsDataManager 组件未加载');
                }
                if (typeof DoneLotsFilterUI === 'undefined') {
                    throw new Error('DoneLotsFilterUI 组件未加载');
                }
                if (typeof DoneLotsPagination === 'undefined') {
                    throw new Error('DoneLotsPagination 组件未加载');
                }
                
                updateTestStatus('组件检查通过，开始初始化...', 'info');
                
                // 初始化数据管理器
                dataManager = new DoneLotsDataManager();
                addLog('✅ DoneLotsDataManager 初始化成功');
                
                // 初始化筛选UI
                filterUI = new DoneLotsFilterUI('filterContainer', dataManager);
                addLog('✅ DoneLotsFilterUI 初始化成功');
                
                // 初始化分页组件
                pagination = new DoneLotsPagination('paginationContainer', dataManager);
                addLog('✅ DoneLotsPagination 初始化成功');
                
                // 设置数据管理器的渲染回调
                dataManager.setRenderCallback(renderData);
                dataManager.setStatisticsCallback(updateStatistics);
                
                // 绑定模式切换事件
                bindModeToggleEvents();
                addLog('✅ 模式切换事件绑定成功');
                
                // 绑定其他UI事件
                bindUIEvents();
                addLog('✅ UI事件绑定成功');
                
                // 使用模拟数据进行测试
                testWithMockData();
                
                updateTestStatus('所有组件初始化完成，开始数据加载测试...', 'success');
                
            } catch (error) {
                updateTestStatus(`初始化失败: ${error.message}`, 'error');
                addLog(`❌ 初始化失败: ${error.message}`, 'error');
                console.error('初始化失败:', error);
            }
        });

        // 使用模拟数据进行测试
        function testWithMockData() {
            addLog('🧪 开始使用模拟数据进行测试...');
            
            // 模拟数据
            const mockData = {
                data: [
                    {
                        id: 1,
                        lot_id: 'LOT001',
                        device: 'Product A',
                        product_name: 'Product A',
                        handler_id: 'HANDLER01',
                        quantity: 1000,
                        priority: 95,
                        score: 8.5,
                        status: 'completed',
                        create_time: new Date().toISOString()
                    },
                    {
                        id: 2,
                        lot_id: 'LOT002',
                        device: 'Product B',
                        product_name: 'Product B',
                        handler_id: 'HANDLER02',
                        quantity: 750,
                        priority: 72,
                        score: 7.2,
                        status: 'running',
                        create_time: new Date().toISOString()
                    },
                    {
                        id: 3,
                        lot_id: 'LOT003',
                        device: 'Product C',
                        product_name: 'Product C',
                        handler_id: 'HANDLER01',
                        quantity: 500,
                        priority: 45,
                        score: 6.8,
                        status: 'failed',
                        create_time: new Date().toISOString()
                    }
                ],
                pagination: {
                    page: 1,
                    size: 50,
                    total: 3,
                    total_pages: 1
                },
                statistics: {
                    totalRecords: 3,
                    totalQuantity: 2250,
                    handlerCount: 2,
                    avgScore: 7.5
                }
            };

            // 设置模拟数据并渲染
            setTimeout(() => {
                renderData(mockData, 'view');
                updateStatistics(mockData.statistics);
                addLog('✅ 模拟数据渲染完成', 'success');
                updateTestStatus('模拟数据测试完成，所有组件工作正常', 'success');
            }, 1000);
        }

        // 绑定模式切换事件
        function bindModeToggleEvents() {
            const viewModeBtn = document.getElementById('viewModeBtn');
            const adjustModeBtn = document.getElementById('adjustModeBtn');
            const finalResultModeBtn = document.getElementById('finalResultModeBtn');
            
            viewModeBtn.addEventListener('change', function() {
                if (this.checked) {
                    switchMode('view');
                }
            });
            
            adjustModeBtn.addEventListener('change', function() {
                if (this.checked) {
                    switchMode('adjust');
                }
            });
            
            finalResultModeBtn.addEventListener('change', function() {
                if (this.checked) {
                    switchMode('final_result');
                }
            });
        }

        // 切换模式
        function switchMode(mode) {
            addLog(`🔄 切换到${mode}模式`);
            
            const tableContainer = document.getElementById('tableContainer');
            const saveBtn = document.getElementById('saveAdjustmentsBtn');
            const cancelBtn = document.getElementById('cancelAdjustmentsBtn');
            
            // 显示/隐藏相应容器和按钮
            if (mode === 'adjust') {
                saveBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'inline-block';
                addLog('📝 调整模式：显示保存和取消按钮');
            } else {
                saveBtn.style.display = 'none';
                cancelBtn.style.display = 'none';
            }
            
            // 在实际应用中，这里会调用 dataManager.setMode(mode)
            addLog(`✅ 模式切换到 ${mode} 成功`);
        }

        // 绑定UI事件
        function bindUIEvents() {
            // 全选复选框
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#tableBody input[type="checkbox"]');
                checkboxes.forEach(cb => cb.checked = this.checked);
                addLog(`${this.checked ? '全选' : '取消全选'}所有项目`);
            });
            
            // 保存调整按钮
            document.getElementById('saveAdjustmentsBtn').addEventListener('click', function() {
                addLog('💾 保存调整按钮点击', 'success');
                showNotification('调整已保存（测试模式）', 'success');
            });
            
            // 取消调整按钮
            document.getElementById('cancelAdjustmentsBtn').addEventListener('click', function() {
                addLog('❌ 取消调整按钮点击', 'warning');
                showNotification('调整已取消（测试模式）', 'info');
            });
        }

        // 渲染数据
        function renderData(data, mode) {
            addLog(`📊 渲染${mode}模式数据: ${data.data?.length || 0}条记录`);
            
            showLoading(false);
            
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';
            
            if (!data.data || data.data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">暂无数据</td></tr>';
                return;
            }
            
            data.data.forEach(item => {
                const row = createTableRow(item, mode);
                tbody.appendChild(row);
            });
            
            addLog('✅ 表格数据渲染完成');
        }

        // 创建表格行
        function createTableRow(item, mode) {
            const row = document.createElement('tr');
            row.dataset.id = item.id || item.lot_id;
            
            const priorityClass = item.priority > 80 ? 'priority-high' : 
                                 item.priority > 50 ? 'priority-medium' : 'priority-low';
            
            const statusClass = item.status === 'completed' ? 'status-success' :
                               item.status === 'running' ? 'status-warning' :
                               item.status === 'failed' ? 'status-danger' : 'status-info';
            
            row.innerHTML = `
                <td>
                    <input type="checkbox" class="row-checkbox" value="${item.id || item.lot_id}">
                </td>
                <td class="${priorityClass}">${item.priority || '-'}</td>
                <td><strong>${item.lot_id || '-'}</strong></td>
                <td>${item.device || item.product_name || '-'}</td>
                <td>${item.handler_id || '-'}</td>
                <td class="text-end">${formatNumber(item.quantity || 0)}</td>
                <td class="text-center">${(item.score || 0).toFixed(1)}</td>
                <td>
                    <span class="status-badge ${statusClass}">
                        ${getStatusText(item.status)}
                    </span>
                </td>
                <td>${formatDateTime(item.create_time || item.CREATE_TIME)}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewDetail('${item.id || item.lot_id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="editItem('${item.id || item.lot_id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteItem('${item.id || item.lot_id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            `;
            
            return row;
        }

        // 更新统计信息
        function updateStatistics(stats) {
            document.getElementById('totalRecords').textContent = stats.totalRecords || 0;
            document.getElementById('totalQuantity').textContent = formatNumber(stats.totalQuantity || 0);
            document.getElementById('handlerCount').textContent = stats.handlerCount || 0;
            document.getElementById('avgScore').textContent = (stats.avgScore || 0).toFixed(1);
            
            addLog(`📈 统计信息更新: ${stats.totalRecords}条记录, ${stats.totalQuantity}总量`);
        }

        // 显示加载状态
        function showLoading(show = true) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = show ? 'flex' : 'none';
        }

        // 刷新数据
        function refreshData() {
            addLog('🔄 刷新数据按钮点击');
            showLoading(true);
            showNotification('刷新数据中...（测试模式）', 'info');
            setTimeout(() => {
                showLoading(false);
                addLog('✅ 数据刷新完成（模拟）');
            }, 1500);
        }

        // 导出数据
        function exportData() {
            addLog('📥 导出数据按钮点击');
            showNotification('导出请求已提交...（测试模式）', 'info');
        }

        // 测试操作函数
        function viewDetail(id) {
            addLog(`👁️ 查看详情: ${id}`);
        }

        function editItem(id) {
            addLog(`✏️ 编辑项目: ${id}`);
        }

        function deleteItem(id) {
            if (confirm('确定要删除这个项目吗？（测试模式）')) {
                addLog(`🗑️ 删除项目: ${id}`);
                showNotification(`项目 ${id} 已删除（测试模式）`, 'success');
            }
        }

        // 工具函数
        function formatNumber(num) {
            return new Intl.NumberFormat('zh-CN').format(num);
        }

        function formatDateTime(dateStr) {
            if (!dateStr) return '-';
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function getStatusText(status) {
            const statusMap = {
                'completed': '已完成',
                'running': '进行中',
                'failed': '失败',
                'scheduled': '已排产'
            };
            return statusMap[status] || status || '未知';
        }

        function showNotification(message, type = 'info') {
            addLog(`📢 通知: ${message}`, type);
            
            // 简单的通知实现
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '9999';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>