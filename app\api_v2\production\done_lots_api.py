#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已排产批次API接口
提供lotprioritydone表的数据查询、操作接口

🚀 缓存统一化修复版本：
- 使用APIDataCacheAdapter替代直接数据库查询
- 提升性能，降低数据库负载
- 保持API接口兼容性
"""

import logging
import os
import re
from datetime import datetime, timedelta

# 🚀 缓存系统集成
from app.utils.api_cache_adapter import get_cached_table_data
from app.utils.simple_cache import cache_result, cache_get, cache_set
from flask import Blueprint, request, jsonify
from sqlalchemy import text
from app import db
from app.utils.api_config import get_api_route
from flask_login import login_required
# 🚀 新增：统一缓存适配器
from app.utils.api_cache_adapter import get_api_cache_adapter, get_cached_table_data
# 连接池接口（保留作为降级方案）
from app.utils.db_helper import get_mysql_connection

logger = logging.getLogger(__name__)

def _get_mapped_stage(stage):
    """浏览接口禁用工序映射，直接返回原始STAGE"""
    # ⚠️ 仅在排产服务内部启用映射，浏览数据接口不做任何转换
    return stage

def _get_historical_equipment_recommendation(device, stage):
    """
    🔥 高优先级任务2：基于历史生产记录的设备推荐（缓存优化版）
    按照最新生产时间（CREATE_TIME）降序排列历史记录，而不是按生产次数
    只参考最近6个月内的历史生产记录，过期记录不作为参考
    使用STAGE映射表进行工序名称转换
    
    🚀 缓存优化：使用APIDataCacheAdapter替代直接数据库查询
    """
    if not device or not stage:
        return None

    try:
        # 获取映射后的工序名称
        mapped_stage = _get_mapped_stage(stage)
        
        # 🚀 使用缓存适配器获取历史设备数据
        api_cache = get_api_cache_adapter()
        historical_data = api_cache.get_historical_equipment_data(device, mapped_stage, days_limit=180)
        
        if not historical_data:
            return None
        
        # 处理历史数据，按最新生产时间排序
        equipment_stats = {}
        for record in historical_data:
            handler_id = record.get('AUXILIARY_EQP_ID')
            if not handler_id or handler_id == '':
                continue
                
            create_time = record.get('CREATE_TIME')
            first_pass_yield = record.get('FIRST_PASS_YIELD')
            final_yield = record.get('FINAL_YIELD')
            
            # 跳过无效数据
            if not first_pass_yield or first_pass_yield == '':
                continue
                
            if handler_id not in equipment_stats:
                equipment_stats[handler_id] = {
                    'production_count': 0,
                    'last_production_time': None,
                    'first_production_time': None,
                    'yields': []
                }
            
            stats = equipment_stats[handler_id]
            stats['production_count'] += 1
            stats['yields'].append({
                'first_pass': first_pass_yield,
                'final': final_yield
            })
            
            # 更新时间统计
            if isinstance(create_time, datetime):
                if stats['last_production_time'] is None or create_time > stats['last_production_time']:
                    stats['last_production_time'] = create_time
                if stats['first_production_time'] is None or create_time < stats['first_production_time']:
                    stats['first_production_time'] = create_time
        
        if not equipment_stats:
            return None
        
        # 按最新生产时间排序，获取推荐设备
        sorted_equipment = sorted(
            equipment_stats.items(),
            key=lambda x: (x[1]['last_production_time'] or datetime.min, x[1]['production_count']),
            reverse=True
        )
        
        # 获取最佳推荐
        handler_id, stats = sorted_equipment[0]
        production_count = stats['production_count']
        last_time = stats['last_production_time']
        
        # 计算平均良率
        yields = stats['yields']
        avg_first_pass_yield = None
        avg_final_yield = None
        
        if yields:
            valid_first_yields = [float(y['first_pass']) for y in yields if y['first_pass'] and y['first_pass'] != '']
            valid_final_yields = [float(y['final']) for y in yields if y['final'] and y['final'] != '']
            
            if valid_first_yields:
                avg_first_pass_yield = sum(valid_first_yields) / len(valid_first_yields)
            if valid_final_yields:
                avg_final_yield = sum(valid_final_yields) / len(valid_final_yields)
        
        # 🚀 使用缓存获取设备状态
        equipment_result = get_cached_table_data('eqp_status', filters=[
            {'field': 'HANDLER_ID', 'operator': 'equals', 'value': handler_id}
        ])
        
        status = None
        if equipment_result.get('success') and equipment_result.get('data'):
            equipment_info = equipment_result['data'][0]
            status = equipment_info.get('STATUS')
        
        # 格式化时间显示
        if isinstance(last_time, datetime):
            time_str = last_time.strftime('%Y-%m-%d')
        else:
            time_str = str(last_time)[:10] if last_time else '未知'

        # 格式化良率显示（优先显示最终良率）
        if avg_final_yield is not None:
            yield_str = f"最终良率: {avg_final_yield*100:.1f}%"
        elif avg_first_pass_yield is not None:
            yield_str = f"首通良率: {avg_first_pass_yield*100:.1f}%"
        else:
            yield_str = "良率: 未知"

        # 判断设备可用性
        if status:
            available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
            is_available = status in available_statuses
            availability_text = "可用" if is_available else f"当前状态: {status}"
        else:
            availability_text = "设备状态未知"

        return f"💡 历史推荐: {handler_id} (最后生产: {time_str}, 历史成功: {production_count}次, {yield_str}, {availability_text})"

    except Exception as e:
        logger.error(f"获取历史设备推荐失败: {e}")
        # 🚀 降级机制：如果缓存失败，使用原有逻辑
        try:
            return _get_historical_equipment_recommendation_legacy(device, stage)
        except Exception as fallback_error:
            logger.error(f"降级方案也失败: {fallback_error}")
            return None

def _get_historical_equipment_recommendation_legacy(device, stage):
    """
    历史设备推荐的降级方案（原有直接查询逻辑）
    仅在缓存方案失败时使用
    """
    if not device or not stage:
        return None

    try:
        mapped_stage = _get_mapped_stage(stage)
        six_months_ago = datetime.now() - timedelta(days=180)

        query = text("""
            SELECT AUXILIARY_EQP_ID as HANDLER_ID, COUNT(*) as production_count,
                   MAX(CREATE_TIME) as last_production_time,
                   MIN(CREATE_TIME) as first_production_time,
                   AVG(CAST(FIRST_PASS_YIELD as DECIMAL(5,4))) as avg_first_pass_yield,
                   AVG(CAST(FINAL_YIELD as DECIMAL(5,4))) as avg_final_yield
            FROM ct
            WHERE DEVICE = :device AND STAGE = :stage
            AND CREATE_TIME >= :six_months_ago
            AND AUXILIARY_EQP_ID IS NOT NULL
            AND AUXILIARY_EQP_ID != ''
            AND FIRST_PASS_YIELD IS NOT NULL
            AND FIRST_PASS_YIELD != ''
            GROUP BY AUXILIARY_EQP_ID
            ORDER BY MAX(CREATE_TIME) DESC, COUNT(*) DESC
            LIMIT 3
        """)

        result = db.session.execute(query, {
            'device': device,
            'stage': mapped_stage,
            'six_months_ago': six_months_ago
        })

        historical_records = result.fetchall()

        if historical_records:
            top_record = historical_records[0]
            handler_id = top_record[0]
            production_count = top_record[1]
            last_time = top_record[2]
            avg_first_pass_yield = top_record[4]
            avg_final_yield = top_record[5]

            status_query = text("""
                SELECT STATUS, HANDLER_CONFIG, EQP_CLASS
                FROM eqp_status
                WHERE HANDLER_ID = :handler_id
            """)

            status_result = db.session.execute(status_query, {'handler_id': handler_id})
            status_record = status_result.fetchone()

            if status_record:
                status = status_record[0]
                available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
                is_available = status in available_statuses

                if isinstance(last_time, datetime):
                    time_str = last_time.strftime('%Y-%m-%d')
                else:
                    time_str = str(last_time)[:10] if last_time else '未知'

                if avg_final_yield is not None:
                    yield_str = f"最终良率: {avg_final_yield*100:.1f}%"
                elif avg_first_pass_yield is not None:
                    yield_str = f"首通良率: {avg_first_pass_yield*100:.1f}%"
                else:
                    yield_str = "良率: 未知"

                availability_text = "可用" if is_available else f"当前状态: {status}"
                return f"💡 历史推荐: {handler_id} (最后生产: {time_str}, 历史成功: {production_count}次, {yield_str}, {availability_text})"

        return None

    except Exception as e:
        logger.error(f"降级方案获取历史设备推荐失败: {e}")
        return None

def generate_suggestion(failure_reason, failure_details, device=None, stage=None):
    """
    生成失败原因的建议解决方案
    🔥 高优先级任务2：优化历史生产记录参考逻辑
    """
    base_suggestion = ""

    if "配置需求获取失败" in failure_reason:
        base_suggestion = "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
    elif "无合适设备" in failure_reason:
        base_suggestion = "请检查设备状态和配置匹配，确保有可用的设备"
    elif "设备ID无效" in failure_reason:
        base_suggestion = "请检查设备配置，确保HANDLER_ID字段正确"
    elif "算法执行异常" in failure_reason:
        base_suggestion = "请检查系统日志，可能需要技术支持"
    elif "测试规范缺失" in failure_reason:
        base_suggestion = "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
    elif "配置" in failure_reason or "config" in failure_reason.lower():
        base_suggestion = "请检查配置设置和参数是否正确"
    elif "设备" in failure_reason or "不兼容" in failure_reason:
        base_suggestion = "请检查设备兼容性和状态"
    else:
        base_suggestion = "请联系技术支持进行详细分析"

    # 浏览接口性能优化：不再实时调用历史生产记录
    return base_suggestion

# 创建蓝图 - 使用配置化的URL前缀
done_lots_bp = Blueprint('done_lots_api', __name__)

# 兼容性导出
done_lots_api = done_lots_bp

@done_lots_bp.route(get_api_route('production/done-lots'), methods=['GET'])
def get_lotprioritydone_data():
    """
    获取已排产表数据 - 重构版本
    支持复杂筛选条件、分页、排序
    使用统一数据服务和连接池
    """
    try:
        # 导入数据服务
        from app.services.done_lots_data_service import DoneLotsDataService
        service = DoneLotsDataService()
        
        # 获取基础请求参数
        table = request.args.get('table', 'lotprioritydone')
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        
        # 只处理lotprioritydone表的请求
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        # 构建复杂筛选条件
        filters = {}
        
        # 全局搜索
        search = request.args.get('search', '').strip()
        if search:
            filters['global_search'] = search
        
        # 具体字段筛选
        lot_id_filter = request.args.get('lot_id', '').strip()
        if lot_id_filter:
            filters['lot_id'] = lot_id_filter
        
        device_filter = request.args.get('device', '').strip()
        if device_filter:
            filters['device'] = device_filter
        
        handler_id_filter = request.args.get('handler_id', '').strip()
        if handler_id_filter:
            filters['handler_id'] = handler_id_filter
        
        stage_filter = request.args.get('stage', '').strip()
        if stage_filter:
            filters['stage'] = stage_filter
        
        # 优先级范围筛选
        priority_min = request.args.get('priority_min')
        priority_max = request.args.get('priority_max')
        if priority_min and priority_max:
            try:
                filters['priority_range'] = [int(priority_min), int(priority_max)]
            except ValueError:
                pass
        
        # 日期范围筛选
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        if date_start and date_end:
            filters['date_range'] = [date_start, date_end]
        
        # 状态筛选
        status_filter = request.args.get('status', '').strip()
        if status_filter:
            filters['status'] = status_filter
        
        # 构建排序条件
        sort_by = request.args.get('sort_by', 'PRIORITY')
        sort_order = request.args.get('sort_order', 'ASC')
        sort = {
            'field': sort_by,
            'order': sort_order
        }
        
        # 构建分页条件
        pagination = {
            'page': page,
            'size': size,
            'get_total': True
        }
        
        # 🚀 使用统一数据服务进行查询
        result = service.get_done_lots_data(
            filters=filters,
            sort=sort,
            pagination=pagination
        )
        
        if not result['success']:
            logger.error(f"❌ 数据服务查询失败: {result.get('error')}")
            return jsonify({
                'success': False,
                'message': result.get('error', '数据服务查询失败')
            }), 500
        
        # 转换数据格式以兼容前端
        records = []
        for item in result['data']:
            record = {
                'id': item.get('id'),
                'PRIORITY': item.get('PRIORITY', ''),
                'HANDLER_ID': item.get('HANDLER_ID', ''),
                'LOT_ID': item.get('LOT_ID', ''),
                'LOT_TYPE': item.get('LOT_TYPE', ''),
                'GOOD_QTY': item.get('GOOD_QTY', 0),
                'PROD_ID': item.get('PROD_ID', ''),
                'DEVICE': item.get('DEVICE', ''),
                'CHIP_ID': item.get('CHIP_ID', ''),
                'PKG_PN': item.get('PKG_PN', ''),
                'PO_ID': item.get('PO_ID', ''),
                'STAGE': item.get('STAGE', ''),
                'STEP': item.get('STEP', ''),
                'WIP_STATE': item.get('WIP_STATE', ''),
                'PROC_STATE': item.get('PROC_STATE', ''),
                'HOLD_STATE': item.get('HOLD_STATE', 0),
                'FLOW_ID': item.get('FLOW_ID', ''),
                'FLOW_VER': item.get('FLOW_VER', ''),
                'RELEASE_TIME': item.get('RELEASE_TIME', ''),
                'FAC_ID': item.get('FAC_ID', ''),
                'CREATE_TIME': item.get('CREATE_TIME', ''),
                'comprehensive_score': item.get('comprehensive_score', 0.0),
                'processing_time': item.get('processing_time', 0.0),
                'changeover_time': item.get('changeover_time', 0.0),
                'algorithm_version': item.get('algorithm_version', ''),
                'match_type': item.get('match_type', ''),
                'priority_score': item.get('priority_score', 0.0)
            }
            records.append(record)
        
        # 计算分页信息
        total = result.get('total_count', 0)
        total_pages = (total + size - 1) // size
        
        logger.info(f"📊 已排产表查询(数据服务): 第{page}页, {size}条/页, 共{total}条记录, 筛选条件数:{result.get('filters_applied', 0)}")
        
        return jsonify({
            'success': True,
            'data': records,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'total_pages': total_pages
            },
            'filters': {
                'search': search,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'filters_applied': result.get('filters_applied', 0)
            },
            'performance': result.get('performance', {})
        })
        
    except Exception as e:
        logger.error(f"❌ 获取已排产数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/columns'), methods=['GET'])
def get_lotprioritydone_columns():
    """获取已排产表的列信息"""
    try:
        table = request.args.get('table', 'lotprioritydone')
        
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        # 定义已排产表的列信息（包含算法扩展字段）
        columns = [
            {'name': 'PRIORITY', 'label': '执行优先级', 'type': 'number', 'sortable': True},
            {'name': 'comprehensive_score', 'label': '综合评分', 'type': 'number', 'sortable': True},
            {'name': 'HANDLER_ID', 'label': '分选机编号', 'type': 'string', 'sortable': True},
            {'name': 'LOT_ID', 'label': '内部工单号', 'type': 'string', 'sortable': True},
            {'name': 'LOT_TYPE', 'label': '批次类型', 'type': 'string', 'sortable': True},
            {'name': 'GOOD_QTY', 'label': '良品数量', 'type': 'number', 'sortable': True},
            {'name': 'PROD_ID', 'label': '产品ID', 'type': 'string', 'sortable': True},
            {'name': 'DEVICE', 'label': '产品名称', 'type': 'string', 'sortable': True},
            {'name': 'CHIP_ID', 'label': '芯片名称', 'type': 'string', 'sortable': True},
            {'name': 'PKG_PN', 'label': '封装', 'type': 'string', 'sortable': True},
            {'name': 'PO_ID', 'label': '订单号', 'type': 'string', 'sortable': True},
            {'name': 'STAGE', 'label': '工序', 'type': 'string', 'sortable': True},
            {'name': 'STEP', 'label': '工步', 'type': 'string', 'sortable': True},
            {'name': 'processing_time', 'label': '预计加工时间(h)', 'type': 'number', 'sortable': True},
            {'name': 'changeover_time', 'label': '改机时间(min)', 'type': 'number', 'sortable': True},
            {'name': 'WIP_STATE', 'label': 'WIP状态', 'type': 'string', 'sortable': True},
            {'name': 'PROC_STATE', 'label': '流程状态', 'type': 'string', 'sortable': True},
            {'name': 'HOLD_STATE', 'label': '扣留状态', 'type': 'number', 'sortable': True},
            {'name': 'FLOW_ID', 'label': '流程ID', 'type': 'string', 'sortable': True},
            {'name': 'FLOW_VER', 'label': '流程版本', 'type': 'string', 'sortable': True},
            {'name': 'RELEASE_TIME', 'label': '释放时间', 'type': 'datetime', 'sortable': True},
            {'name': 'FAC_ID', 'label': '工厂ID', 'type': 'string', 'sortable': True},
            {'name': 'CREATE_TIME', 'label': '创建时间', 'type': 'datetime', 'sortable': True},
            {'name': 'priority_score', 'label': '优先级评分', 'type': 'number', 'sortable': True},
            {'name': 'algorithm_version', 'label': '算法版本', 'type': 'string', 'sortable': True},
            {'name': 'match_type', 'label': '匹配类型', 'type': 'string', 'sortable': True}
        ]
        
        return jsonify({
            'success': True,
            'columns': columns
        })
        
    except Exception as e:
        logger.error(f"❌ 获取列信息失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取列信息失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/move-to-waiting'), methods=['POST'])
def move_lots_to_waiting():
    """将已排产批次移回待排产状态"""
    try:
        data = request.get_json()
        lot_ids = data.get('ids', [])
        
        if not lot_ids:
            return jsonify({
                'success': False,
                'message': '请选择要移动的批次'
            }), 400
        
        # 获取要移动的批次信息
        placeholders = ','.join([':id_%d' % i for i in range(len(lot_ids))])
        params = {'id_%d' % i: lot_id for i, lot_id in enumerate(lot_ids)}
        
        select_query = text(f"""
            SELECT LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                   FLOW_ID, FLOW_VER, FAC_ID, RELEASE_TIME
            FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(select_query, params)
        lots_to_move = result.fetchall()
        
        if not lots_to_move:
            return jsonify({
                'success': False,
                'message': '未找到指定的批次'
            }), 404
        
        # 将批次信息重新插入到ET_WAIT_LOT表
        moved_count = 0
        for lot in lots_to_move:
            try:
                insert_query = text("""
                    INSERT INTO ET_WAIT_LOT (
                        LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        FAC_ID, RELEASE_TIME, CREATE_TIME
                    ) VALUES (
                        :lot_id, :device, :stage, :good_qty, :pkg_pn, :chip_id,
                        'WAIT', 'UNASSIGNED', 0, :flow_id, :flow_ver,
                        :fac_id, :release_time, NOW()
                    )
                """)
                
                db.session.execute(insert_query, {
                    'lot_id': lot[0],
                    'device': lot[1],
                    'stage': lot[2],
                    'good_qty': lot[3],
                    'pkg_pn': lot[4],
                    'chip_id': lot[5],
                    'flow_id': lot[6],
                    'flow_ver': lot[7],
                    'fac_id': lot[8],
                    'release_time': lot[9]
                })
                moved_count += 1
                
            except Exception as e:
                logger.warning(f"移动批次 {lot[0]} 失败: {e}")
                continue
        
        # 从已排产表中删除这些批次
        if moved_count > 0:
            delete_query = text(f"""
                DELETE FROM lotprioritydone 
                WHERE PRIORITY IN ({placeholders})
            """)
            db.session.execute(delete_query, params)
        
        db.session.commit()
        
        logger.info(f"✅ 成功移动 {moved_count} 个批次到待排产状态")
        
        return jsonify({
            'success': True,
            'message': f'成功移动 {moved_count} 个批次到待排产状态',
            'moved_count': moved_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 移动批次失败: {e}")
        return jsonify({
            'success': False,
            'message': f'移动失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/delete'), methods=['POST'])
def delete_lotprioritydone_records():
    """删除已排产记录"""
    try:
        data = request.get_json()
        table = data.get('table', 'lotprioritydone')
        ids = data.get('ids', [])
        
        if table != 'lotprioritydone':
            return jsonify({
                'success': False,
                'message': f'不支持的表名: {table}'
            }), 400
        
        if not ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的记录'
            }), 400
        
        # 构建删除语句
        placeholders = ','.join([':id_%d' % i for i in range(len(ids))])
        params = {'id_%d' % i: record_id for i, record_id in enumerate(ids)}
        
        delete_query = text(f"""
            DELETE FROM lotprioritydone 
            WHERE PRIORITY IN ({placeholders})
        """)
        
        result = db.session.execute(delete_query, params)
        deleted_count = result.rowcount
        
        db.session.commit()
        
        logger.info(f"✅ 成功删除 {deleted_count} 条已排产记录")
        
        return jsonify({
            'success': True,
            'message': f'成功删除 {deleted_count} 条记录',
            'deleted_count': deleted_count
        })
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"❌ 删除已排产记录失败: {e}")
        return jsonify({
            'success': False,
            'message': f'删除失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/save-and-publish-schedule'), methods=['POST'])
@login_required
def save_and_publish_schedule():
    """保存并发布排产结果"""
    try:
        data = request.get_json()
        schedule = data.get('schedule', [])
        metrics = data.get('metrics', {})
        publish_status = data.get('publish_status', 'DRAFT')
        
        if not schedule:
            return jsonify({
                'success': False,
                'message': '没有可保存的排产数据'
            }), 400
        
        saved_count = 0
        
        try:
            # 如果是发布状态，先清空现有数据
            if publish_status == 'PUBLISHED':
                delete_query = text("DELETE FROM lotprioritydone")
                db.session.execute(delete_query)
                logger.info("✅ 已清空现有排产数据，准备保存新数据")
            
            # 批量插入新数据
            for item in schedule:
                insert_query = text("""
                    INSERT INTO lotprioritydone (
                        PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                        PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        RELEASE_TIME, FAC_ID, CREATE_TIME,
                        comprehensive_score, processing_time, changeover_time,
                        algorithm_version, match_type, priority_score
                    ) VALUES (
                        :priority, :handler_id, :lot_id, :lot_type, :good_qty,
                        :prod_id, :device, :chip_id, :pkg_pn, :po_id, :stage,
                        :wip_state, :proc_state, :hold_state, :flow_id, :flow_ver,
                        :release_time, :fac_id, NOW(),
                        :comprehensive_score, :processing_time, :changeover_time,
                        :algorithm_version, :match_type, :priority_score
                    )
                """)
                
                insert_data = {
                    'priority': item.get('PRIORITY') or saved_count + 1,
                    'handler_id': item.get('HANDLER_ID', ''),
                    'lot_id': item.get('LOT_ID', ''),
                    'lot_type': item.get('LOT_TYPE', ''),
                    'good_qty': item.get('GOOD_QTY', 0),
                    'prod_id': item.get('PROD_ID', ''),
                    'device': item.get('DEVICE', ''),
                    'chip_id': item.get('CHIP_ID', ''),
                    'pkg_pn': item.get('PKG_PN', ''),
                    'po_id': item.get('PO_ID', ''),
                    'stage': item.get('STAGE', ''),
                    'wip_state': item.get('WIP_STATE', ''),
                    'proc_state': item.get('PROC_STATE', ''),
                    'hold_state': item.get('HOLD_STATE', 0),
                    'flow_id': item.get('FLOW_ID', ''),
                    'flow_ver': item.get('FLOW_VER', ''),
                    'release_time': item.get('RELEASE_TIME') or None,
                    'fac_id': item.get('FAC_ID', ''),
                    'comprehensive_score': item.get('comprehensive_score', 0.0),
                    'processing_time': item.get('processing_time', 0.0),
                    'changeover_time': item.get('changeover_time', 0.0),
                    'algorithm_version': metrics.get('algorithm', 'enhanced_heuristic'),
                    'match_type': item.get('match_type', ''),
                    'priority_score': item.get('priority_score', 0.0)
                }
                
                db.session.execute(insert_query, insert_data)
                saved_count += 1
            
            # 提交事务
            db.session.commit()
            
            logger.info(f"✅ 成功保存并发布 {saved_count} 条排产记录")
            
            # Excel自动保存（如果启用）
            try:
                from app.services.excel_auto_save_service import get_excel_auto_save_service
                excel_service = get_excel_auto_save_service()
                
                if excel_service.is_auto_save_enabled():
                    # 构建用于Excel保存的数据
                    save_result = excel_service.auto_save_schedule_result(
                        schedule_data=schedule,
                        source='publish',
                        metrics=metrics
                    )
                    
                    if save_result.get('success'):
                        logger.info(f"✅ 保存发布排产结果已自动保存为Excel: {save_result.get('filename')} (共{save_result.get('records_count')}条记录)")
                    else:
                        logger.warning(f"⚠️ 保存发布排产结果Excel自动保存失败: {save_result.get('message')}")
                        
            except Exception as excel_error:
                logger.error(f"❌ 保存发布Excel自动保存异常: {excel_error}")
                # Excel保存失败不影响主要流程
            
            return jsonify({
                'success': True,
                'message': f'成功保存并发布 {saved_count} 条排产记录',
                'saved_count': saved_count,
                'publish_status': publish_status
            })
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"❌ 保存排产数据失败: {e}")
            return jsonify({
                'success': False,
                'message': f'保存失败: {str(e)}'
            }), 500
            
    except Exception as e:
        logger.error(f"❌ 保存并发布排产结果API异常: {e}")
        return jsonify({
            'success': False,
            'message': f'API异常: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/failed'), methods=['GET'])
def get_failed_lots_data():
    """
    获取失败批次数据 - 重构版本
    支持复杂筛选条件、分页、排序
    使用统一数据服务和连接池
    """
    try:
        # 导入数据服务
        from app.services.done_lots_data_service import DoneLotsDataService
        service = DoneLotsDataService()
        
        # 获取请求参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        hours_limit = int(request.args.get('hours', '24'))
        
        # 构建筛选条件
        filters = {
            'hours_limit': hours_limit if current_only else None
        }
        
        # 添加其他筛选条件
        stage_filter = request.args.get('stage', '').strip()
        if stage_filter:
            filters['stage'] = stage_filter
            
        lot_type_filter = request.args.get('lot_type', '').strip()
        if lot_type_filter:
            filters['lot_type'] = lot_type_filter
            
        failure_type_filter = request.args.get('failure_type', '').strip()
        if failure_type_filter:
            filters['failure_type'] = failure_type_filter
            
        search = request.args.get('search', '').strip()
        if search:
            filters['search'] = search
        
        # 构建排序条件
        sort_by = request.args.get('sort_by', 'timestamp')
        sort_order = request.args.get('sort_order', 'DESC')
        sort = {
            'field': sort_by,
            'order': sort_order
        }
        
        # 构建分页条件（如果需要）
        pagination = None
        if request.args.get('paginated', 'false').lower() == 'true':
            pagination = {
                'page': page,
                'size': size,
                'get_total': True
            }
        
        # 🚀 使用统一数据服务进行查询（失败批次不支持排序和分页）
        result = service.get_failed_lots_data(
            filters=filters
        )
        
        if not result['success']:
            logger.error(f"❌ 失败批次数据服务查询失败: {result.get('error')}")
            return jsonify({
                'success': False,
                'message': result.get('error', '失败批次数据服务查询失败')
            }), 500
        
        # 计算统计信息
        failed_lots = result['data']
        summary_stats = {
            'total_failed': len(failed_lots),
            'config_missing': sum(1 for lot in failed_lots if '配置' in lot.get('failure_reason', '') or 'config' in lot.get('failure_reason', '').lower()),
            'equipment_incompatible': sum(1 for lot in failed_lots if '设备' in lot.get('failure_reason', '') or '不兼容' in lot.get('failure_reason', '')),
            'other_reasons': 0
        }
        summary_stats['other_reasons'] = summary_stats['total_failed'] - summary_stats['config_missing'] - summary_stats['equipment_incompatible']
        
        logger.info(f"📊 失败批次查询(数据服务): 共{len(failed_lots)}条记录, 筛选条件: {filters}")
        
        return jsonify({
            'success': True,
            'data': {
                'failed_lots': failed_lots,
                'total_count': len(failed_lots),
                'summary': summary_stats
            },
            'debug_info': {
                'data_source': 'done_lots_data_service',
                'table_name': 'scheduling_failed_lots',
                'total_records': len(failed_lots),
                'record_count': len(failed_lots),
                'filters_applied': len([k for k, v in filters.items() if v])
            },
            'performance': result.get('performance', {})
        })
        
    except Exception as e:
        logger.error(f"❌ 获取失败批次数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}',
            'data': {
                'failed_lots': [],
                'total_count': 0,
                'summary': {
                    'total_failed': 0,
                    'config_missing': 0,
                    'equipment_incompatible': 0,
                    'other_reasons': 0
                }
            }
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/final-result'), methods=['GET'])
def get_final_result_data():
    """
    获取最终结果数据 - 重构版本
    支持复杂筛选条件、分页、排序
    使用统一数据服务和连接池
    """
    try:
        # 导入数据服务
        from app.services.done_lots_data_service import DoneLotsDataService
        service = DoneLotsDataService()
        
        # 获取基础请求参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 50))
        
        # 构建复杂筛选条件
        filters = {}
        
        # 全局搜索
        search = request.args.get('search', '').strip()
        if search:
            filters['global_search'] = search
        
        # 具体字段筛选
        lot_id_filter = request.args.get('lot_id', '').strip()
        if lot_id_filter:
            filters['lot_id'] = lot_id_filter
        
        device_filter = request.args.get('device', '').strip()
        if device_filter:
            filters['device'] = device_filter
        
        handler_id_filter = request.args.get('handler_id', '').strip()
        if handler_id_filter:
            filters['handler_id'] = handler_id_filter
        
        stage_filter = request.args.get('stage', '').strip()
        if stage_filter:
            filters['stage'] = stage_filter
        
        # 优先级范围筛选
        priority_min = request.args.get('priority_min')
        priority_max = request.args.get('priority_max')
        if priority_min and priority_max:
            try:
                filters['priority_range'] = [int(priority_min), int(priority_max)]
            except ValueError:
                pass
        
        # 日期范围筛选
        date_start = request.args.get('date_start')
        date_end = request.args.get('date_end')
        if date_start and date_end:
            filters['date_range'] = [date_start, date_end]
        
        # 状态筛选
        status_filter = request.args.get('status', '').strip()
        if status_filter:
            filters['status'] = status_filter
        
        # 构建排序条件（映射字段名到数据库字段）
        sort_by = request.args.get('sort_by', 'PRIORITY')
        sort_order = request.args.get('sort_order', 'ASC')
        
        # 字段名映射（前端使用大写，数据库使用小写）
        field_mapping = {
            'PRIORITY': 'priority',
            'CREATE_TIME': 'create_time',
            'LOT_ID': 'lot_id',
            'DEVICE': 'device',
            'HANDLER_ID': 'handler_id',
            'GOOD_QTY': 'good_qty',
            'STAGE': 'stage',
            'STEP': 'step'
        }
        
        mapped_field = field_mapping.get(sort_by, 'priority')
        sort = {
            'field': mapped_field,
            'order': sort_order
        }
        
        # 构建分页条件
        pagination = {
            'page': page,
            'size': size,
            'get_total': True
        }
        
        # 🚀 使用统一数据服务进行查询
        result = service.get_final_result_data(
            filters=filters,
            sort=sort,
            pagination=pagination
        )
        
        if not result['success']:
            logger.info(f"ℹ️ 最终结果数据查询: {result.get('message', '表可能不存在')}")
            return jsonify({
                'success': True,  # 返回成功但数据为空
                'data': [],
                'pagination': {
                    'page': page,
                    'size': size,
                    'total': 0,
                    'total_pages': 0
                },
                'message': result.get('message', '最终结果表暂无数据或不存在')
            })
        
        # 转换数据格式以兼容前端
        records = []
        for item in result['data']:
            record = {
                'id': item.get('id'),
                'PRIORITY': item.get('PRIORITY', ''),
                'HANDLER_ID': item.get('HANDLER_ID', ''),
                'LOT_ID': item.get('LOT_ID', ''),
                'LOT_TYPE': item.get('LOT_TYPE', ''),
                'GOOD_QTY': item.get('GOOD_QTY', 0),
                'PROD_ID': item.get('PROD_ID', ''),
                'DEVICE': item.get('DEVICE', ''),
                'CHIP_ID': item.get('CHIP_ID', ''),
                'PKG_PN': item.get('PKG_PN', ''),
                'PO_ID': item.get('PO_ID', ''),
                'STAGE': item.get('STAGE', ''),
                'STEP': item.get('STEP', ''),
                'WIP_STATE': item.get('WIP_STATE', ''),
                'PROC_STATE': item.get('PROC_STATE', ''),
                'HOLD_STATE': item.get('HOLD_STATE', 0),
                'FLOW_ID': item.get('FLOW_ID', ''),
                'FLOW_VER': item.get('FLOW_VER', ''),
                'RELEASE_TIME': item.get('RELEASE_TIME', ''),
                'FAC_ID': item.get('FAC_ID', ''),
                'CREATE_TIME': item.get('CREATE_TIME', ''),
                'UPDATE_TIME': item.get('UPDATE_TIME', ''),
                'comprehensive_score': item.get('comprehensive_score', 0.0),
                'processing_time': item.get('processing_time', 0.0),
                'changeover_time': item.get('changeover_time', 0.0),
                'algorithm_version': item.get('algorithm_version', ''),
                'match_type': item.get('match_type', ''),
                'priority_score': item.get('priority_score', 0.0)
            }
            records.append(record)
        
        # 计算分页信息
        total = result.get('total_count', 0)
        total_pages = (total + size - 1) // size
        
        logger.info(f"📊 最终结果查询(数据服务): 第{page}页, {size}条/页, 共{total}条记录, 筛选条件数:{result.get('filters_applied', 0)}")
        
        return jsonify({
            'success': True,
            'data': records,
            'pagination': {
                'page': page,
                'size': size,
                'total': total,
                'total_pages': total_pages
            },
            'filters': {
                'search': search,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'filters_applied': result.get('filters_applied', 0)
            },
            'performance': result.get('performance', {})
        })
        
    except Exception as e:
        logger.error(f"❌ 获取最终结果数据失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'查询失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/get-failed-lots-from-logs'), methods=['GET'])
def get_failed_lots_from_logs():
    """从数据库中获取排产失败的批次信息（基于调度会话的优化版本）"""
    try:
        import os
        import re
        from datetime import datetime, timedelta
        from app.utils.db_connection_pool import get_db_connection_context
        
        # 🔥 获取查询参数
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        session_id = request.args.get('session_id', '')  # 新增：特定会话ID
        
        logger.info(f"🔍 开始获取失败批次信息... (current_only={current_only}, session_id={session_id})")
        
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
            
                # 检查表是否存在
                check_table_sql = """
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
                """
                cursor.execute(check_table_sql)
                check_result = cursor.fetchone()
                
                if isinstance(check_result, dict):
                    table_exists = list(check_result.values())[0] > 0
                else:
                    table_exists = check_result[0] > 0
                
                if not table_exists:
                    return jsonify({
                        'success': True,
                        'data': {
                            'failed_lots': [],
                            'total_count': 0,
                            'summary': {
                                'total_failed': 0,
                                'config_missing': 0,
                                'equipment_incompatible': 0,
                                'other_reasons': 0
                            }
                        },
                        'debug_info': {
                            'data_source': 'database_empty',
                            'table_name': 'scheduling_failed_lots',
                            'total_records': 0
                        },
                        'message': 'scheduling_failed_lots表不存在'
                    })
                
                # 🚀 新逻辑：基于调度会话的查询
                failed_lots = []
                
                if current_only:
                    if session_id:
                        # 指定会话ID的失败批次
                        logger.info(f"查询指定会话 {session_id} 的失败批次...")
                        query_sql = """
                        SELECT
                            sfl.lot_id,
                            sfl.device,
                            sfl.stage,
                            sfl.good_qty,
                            sfl.failure_reason,
                            sfl.failure_details,
                            sfl.suggestion,
                            sfl.session_id,
                            sfl.timestamp,
                            ewl.LOT_TYPE,
                            ewl.PKG_PN
                        FROM scheduling_failed_lots sfl
                        LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                        WHERE sfl.session_id = %s
                        ORDER BY sfl.timestamp DESC
                        LIMIT 1000
                        """
                        cursor.execute(query_sql, (session_id,))
                    else:
                        # 🔥 核心优化：获取最新调度会话的失败批次
                        logger.info("查询最新调度会话的失败批次...")
                        
                        # 先获取最新的会话ID
                        latest_session_sql = """
                        SELECT session_id 
                        FROM scheduling_failed_lots 
                        WHERE session_id IS NOT NULL AND session_id != ''
                        ORDER BY timestamp DESC 
                        LIMIT 1
                        """
                        cursor.execute(latest_session_sql)
                        latest_session_result = cursor.fetchone()
                        
                        if latest_session_result:
                            if isinstance(latest_session_result, dict):
                                latest_session_id = list(latest_session_result.values())[0]
                            else:
                                latest_session_id = latest_session_result[0]
                            
                            logger.info(f"找到最新调度会话: {latest_session_id}")
                            
                            # 获取该会话的所有失败批次
                            query_sql = """
                            SELECT
                                sfl.lot_id,
                                sfl.device,
                                sfl.stage,
                                sfl.good_qty,
                                sfl.failure_reason,
                                sfl.failure_details,
                                sfl.suggestion,
                                sfl.session_id,
                                sfl.timestamp,
                                ewl.LOT_TYPE,
                                ewl.PKG_PN
                            FROM scheduling_failed_lots sfl
                            LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                            WHERE sfl.session_id = %s
                            ORDER BY sfl.timestamp DESC
                            LIMIT 1000
                            """
                            cursor.execute(query_sql, (latest_session_id,))
                        else:
                            logger.warning("未找到有效的调度会话")
                            cursor.execute("SELECT NULL WHERE 1=0")  # 返回空结果
                else:
                    # 获取所有历史失败记录
                    logger.info("查询所有历史失败批次...")
                    query_sql = """
                    SELECT
                        sfl.lot_id,
                        sfl.device,
                        sfl.stage,
                        sfl.good_qty,
                        sfl.failure_reason,
                        sfl.failure_details,
                        sfl.suggestion,
                        sfl.session_id,
                        sfl.timestamp,
                        ewl.LOT_TYPE,
                        ewl.PKG_PN
                    FROM scheduling_failed_lots sfl
                    LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                    ORDER BY sfl.timestamp DESC
                    LIMIT 1000
                    """
                    cursor.execute(query_sql)
                
                results = cursor.fetchall()
                logger.info(f"查询到 {len(results)} 条失败批次记录")
                
                if not results:
                    return jsonify({
                        'success': True,
                        'data': {
                            'failed_lots': [],
                            'total_count': 0,
                            'summary': {
                                'total_failed': 0,
                                'config_missing': 0,
                                'equipment_incompatible': 0,
                                'other_reasons': 0
                            }
                        },
                        'debug_info': {
                            'data_source': 'database_empty_current_mode' if current_only else 'database_empty',
                            'table_name': 'scheduling_failed_lots',
                            'total_records': 0
                        },
                        'message': '未找到失败批次数据'
                    })
                
                # 处理查询结果
                config_missing_count = 0
                equipment_incompatible_count = 0
                other_reasons_count = 0
                
                for row in results:
                    if isinstance(row, dict):
                        lot_id = row['lot_id'] or ''
                        device = row['device'] or ''
                        stage = row['stage'] or ''
                        good_qty = row['good_qty'] or 0
                        failure_reason = row['failure_reason'] or '未知原因'
                        failure_details = row['failure_details'] or ''
                        suggestion = row.get('suggestion', '') or generate_suggestion(failure_reason, failure_details, device, stage)
                        session_id = row.get('session_id', '')
                        timestamp = row['timestamp']
                        lot_type = row.get('LOT_TYPE', '') or ''
                        pkg_pn = row.get('PKG_PN', '') or ''
                    else:
                        # 处理元组格式
                        lot_id = row[0] or ''
                        device = row[1] or ''
                        stage = row[2] or ''
                        good_qty = row[3] or 0
                        failure_reason = row[4] or '未知原因'
                        failure_details = row[5] or ''
                        suggestion = row[6] or generate_suggestion(failure_reason, failure_details, device, stage)
                        session_id = row[7] or ''
                        timestamp = row[8]
                        lot_type = row[9] or ''
                        pkg_pn = row[10] or ''
                    
                    # 统计失败原因类型
                    if any(keyword in failure_reason for keyword in ['配置', '规范', 'config', 'spec']):
                        config_missing_count += 1
                    elif any(keyword in failure_reason for keyword in ['设备', '不兼容', 'equipment', 'incompatible', '无合适']):
                        equipment_incompatible_count += 1
                    else:
                        other_reasons_count += 1
                    
                    failure_info = {
                        'LOT_ID': lot_id,
                        'DEVICE': device,
                        'STAGE': stage,
                        'LOT_TYPE': lot_type,
                        'PKG_PN': pkg_pn,
                        'GOOD_QTY': good_qty,
                        'failure_reason': failure_reason,
                        'suggestion': suggestion,
                        'timestamp': timestamp.isoformat() if timestamp else datetime.now().isoformat(),
                        'session_id': session_id
                    }
                    failed_lots.append(failure_info)
                
                # 构建响应
                return jsonify({
                    'success': True,
                    'data': {
                        'failed_lots': failed_lots,
                        'total_count': len(failed_lots),
                        'summary': {
                            'total_failed': len(failed_lots),
                            'config_missing': config_missing_count,
                            'equipment_incompatible': equipment_incompatible_count,
                            'other_reasons': other_reasons_count
                        }
                    },
                    'debug_info': {
                        'data_source': 'database',
                        'table_name': 'scheduling_failed_lots',
                        'total_records': len(failed_lots),
                        'query_mode': 'latest_session' if current_only else 'all_history'
                    },
                    'message': f'成功获取失败批次数据（{"最新调度会话" if current_only else "所有历史"}）'
                })
                
        except Exception as db_error:
            logger.error(f"数据库查询失败: {db_error}")
            return jsonify({
                'success': False,
                'error': str(db_error),
                'message': '数据库查询失败'
            }), 500
            
    except Exception as e:
        logger.error(f"❌ 获取失败批次数据出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取失败批次数据失败'
        }), 500


@done_lots_bp.route(get_api_route('production/get-failed-lots-filter-options'), methods=['GET'])
def get_failed_lots_filter_options():
    """获取失败批次筛选选项的动态数据"""
    try:
        from app.utils.db_connection_pool import get_db_connection_context
        
        logger.info("🔍 获取失败批次筛选选项...")
        
        with get_db_connection_context() as conn:
            cursor = conn.cursor()
        
        # 检查表是否存在
        check_table_sql = """
        SELECT COUNT(*) 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'scheduling_failed_lots'
        """
        cursor.execute(check_table_sql)
        check_result = cursor.fetchone()
        
        # 处理DictCursor返回的字典格式
        if isinstance(check_result, dict):
            table_exists = list(check_result.values())[0] > 0
        else:
            table_exists = check_result[0] > 0
        
        if table_exists:
            # 获取所有不同的工序
            stage_sql = """
            SELECT DISTINCT sfl.stage
            FROM scheduling_failed_lots sfl
            WHERE sfl.stage IS NOT NULL AND sfl.stage != ''
            ORDER BY sfl.stage
            """
            cursor.execute(stage_sql)
            stages = [row['stage'] if isinstance(row, dict) else row[0] for row in cursor.fetchall()]
            
            # 获取所有不同的工单分类（关联ET_WAIT_LOT表）
            lot_type_sql = """
            SELECT DISTINCT ewl.LOT_TYPE
            FROM scheduling_failed_lots sfl
            LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
            WHERE ewl.LOT_TYPE IS NOT NULL AND ewl.LOT_TYPE != ''
            ORDER BY ewl.LOT_TYPE
            """
            cursor.execute(lot_type_sql)
            lot_types = [row['LOT_TYPE'] if isinstance(row, dict) else row[0] for row in cursor.fetchall()]
            
            # 获取所有不同的失败原因
            failure_reason_sql = """
            SELECT DISTINCT sfl.failure_reason
            FROM scheduling_failed_lots sfl
            WHERE sfl.failure_reason IS NOT NULL AND sfl.failure_reason != ''
            ORDER BY sfl.failure_reason
            """
            cursor.execute(failure_reason_sql)
            failure_reasons = [row['failure_reason'] if isinstance(row, dict) else row[0] for row in cursor.fetchall()]
            
            cursor.close()
            # 分析失败原因并生成分类选项
            failure_types = []
            config_keywords = ['配置', '规范', 'config', 'spec']
            equipment_keywords = ['设备', '不兼容', 'equipment', 'incompatible']
            
            has_config_issues = any(any(keyword in reason for keyword in config_keywords) for reason in failure_reasons)
            has_equipment_issues = any(any(keyword in reason for keyword in equipment_keywords) for reason in failure_reasons)
            has_other_issues = len(failure_reasons) > 0
            
            if has_config_issues:
                failure_types.append({'value': '配置', 'label': '配置缺失'})
            if has_equipment_issues:
                failure_types.append({'value': '设备', 'label': '设备不兼容'})
            if has_other_issues:
                failure_types.append({'value': '其他', 'label': '其他原因'})
            
            return jsonify({
                'success': True,
                'data': {
                    'stages': [{'value': stage, 'label': stage} for stage in stages],
                    'lot_types': [{'value': lot_type, 'label': lot_type} for lot_type in lot_types],
                    'failure_types': failure_types,
                    'failure_reasons': failure_reasons
                },
                'message': '成功获取筛选选项'
            })
        else:
            # 表不存在时返回默认选项
            return jsonify({
                'success': True,
                'data': {
                    'stages': [],
                    'lot_types': [],
                    'failure_types': [
                        {'value': '配置', 'label': '配置缺失'},
                        {'value': '设备', 'label': '设备不兼容'},
                        {'value': '其他', 'label': '其他原因'}
                    ],
                    'failure_reasons': []
                },
                'message': '数据表不存在，返回默认选项'
            })
            
    except Exception as e:
        logger.error(f"❌ 获取筛选选项失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取筛选选项失败: {str(e)}'
        }), 500 

@done_lots_bp.route(get_api_route('production/clear-failed-lots'), methods=['POST'])
@login_required
def clear_failed_lots():
    """
    清空排产失败记录 - 优化的死锁重试机制
    """
    import time
    max_retries = 2  # 优化：减少重试次数 3→2
    retry_delay = 0.1  # 固定100ms延迟，移除指数退避
    
    for attempt in range(max_retries):
        try:
            logger.info(f"[DEBUG] 开始清空排产失败记录... (尝试 {attempt + 1}/{max_retries})")
            
            # 获取MySQL连接
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                # 优化：缩短锁等待超时时间 5→3秒
                conn.cursor().execute("SET SESSION innodb_lock_wait_timeout = 3")
                cursor = conn.cursor()
                
                try:
                    # 检查表是否存在
                    check_table_sql = """
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'scheduling_failed_lots'
                    """
                    cursor.execute(check_table_sql)
                    check_result = cursor.fetchone()
                    
                    # 处理DictCursor返回的字典格式
                    if isinstance(check_result, dict):
                        table_exists = list(check_result.values())[0] > 0
                    else:
                        table_exists = check_result[0] > 0
                    
                    if not table_exists:
                        return jsonify({
                            'success': False,
                            'message': 'scheduling_failed_lots表不存在'
                        }), 400
                    
                    # 获取删除前的记录数
                    count_sql = "SELECT COUNT(*) FROM scheduling_failed_lots"
                    cursor.execute(count_sql)
                    count_result = cursor.fetchone()
                    
                    if isinstance(count_result, dict):
                        before_count = list(count_result.values())[0]
                    else:
                        before_count = count_result[0]
                    
                    logger.info(f"[DEBUG] 准备删除 {before_count} 条失败记录")
                    
                    # 如果没有记录，直接返回成功
                    if before_count == 0:
                        logger.info("[INFO] 没有失败记录需要清空")
                        return jsonify({
                            'success': True,
                            'message': '没有失败记录需要清空',
                            'deleted_count': 0
                        })
                    
                    # 优化：简化事务，移除ALTER TABLE操作
                    conn.begin()
                    try:
                        delete_sql = "DELETE FROM scheduling_failed_lots"
                        cursor.execute(delete_sql)
                        
                        # 提交事务
                        conn.commit()
                        logger.info(f"[OK] 成功清空排产失败记录，删除了 {before_count} 条记录")
                        
                        # 🔥 关键修复：清空成功后主动清理所有相关缓存
                        try:
                            from app.utils.simple_cache import cache_clear_category, cache_delete
                            from app.utils.api_cache_adapter import invalidate_api_cache
                            
                            # 1. 清理simple_cache中的失败批次相关缓存
                            cache_keys_to_clear = [
                                'failed_lots_True_24',    # 24小时当前数据
                                'failed_lots_False_24',   # 24小时全部数据
                                'failed_lots_True_168',   # 7天当前数据
                                'failed_lots_False_168',  # 7天全部数据
                                'failed_lots_True_72',    # 3天数据
                                'failed_lots_False_72'    # 3天数据
                            ]
                            
                            cleared_cache_count = 0
                            for cache_key in cache_keys_to_clear:
                                try:
                                    if cache_delete(cache_key):
                                        cleared_cache_count += 1
                                except Exception as cache_err:
                                    logger.warning(f"清理缓存键 {cache_key} 失败: {cache_err}")
                            
                            # 2. 清理API缓存适配器相关缓存
                            try:
                                invalidate_api_cache('scheduling_failed_lots')
                                logger.info("✅ 已清理API缓存适配器中的失败批次缓存")
                            except Exception as api_cache_err:
                                logger.warning(f"清理API缓存失败: {api_cache_err}")
                            
                            logger.info(f"🧹 缓存清理完成: 清理了 {cleared_cache_count} 个Simple Cache键 + API缓存")
                            
                        except Exception as cache_cleanup_error:
                            # 缓存清理失败不影响主要的清空操作
                            logger.error(f"❌ 缓存清理异常（不影响数据清空）: {cache_cleanup_error}")
                        
                        return jsonify({
                            'success': True,
                            'message': f'成功清空排产失败记录，删除了 {before_count} 条记录',
                            'deleted_count': before_count,
                            'cache_cleared': True  # 标记缓存已清理
                        })
                        
                    except Exception as tx_e:
                        conn.rollback()
                        raise tx_e
                        
                finally:
                    # 确保cursor关闭
                    cursor.close()
                    
        except Exception as e:
            error_msg = str(e)
            logger.error(f"[X] 清空排产失败记录失败 (尝试 {attempt + 1}): {error_msg}")
            
            # 检查是否为死锁或锁等待超时错误
            if "Deadlock found" in error_msg or "Lock wait timeout" in error_msg:
                if attempt < max_retries - 1:  # 还有重试机会
                    logger.warning(f"[WARNING] 检测到锁冲突，{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
                    # 优化：使用固定延迟，不再使用指数退避
                    continue
                else:
                    logger.error(f"[ERROR] 重试{max_retries}次后仍然失败")
                    return jsonify({
                        'success': False,
                        'message': f'清空失败: 数据库锁冲突，重试{max_retries}次后仍然失败。请稍后再试或联系管理员'
                    }), 500
            else:
                # 非锁冲突错误，直接返回
                return jsonify({
                    'success': False,
                    'message': f'清空失败: {error_msg}'
                }), 500
    
    # 理论上不会执行到这里
    return jsonify({
        'success': False,
        'message': '清空失败: 未知错误'
    }), 500

@done_lots_bp.route(get_api_route('production/export-schedule-to-path'), methods=['POST'])
@login_required
def export_schedule_to_path():
    """导出排产结果到指定路径的Excel文件"""
    try:
        data = request.get_json()
        export_path = data.get('export_path', '')
        
        if not export_path:
            return jsonify({
                'success': False,
                'message': '请指定导出路径'
            }), 400
        
        import os
        import pandas as pd
        from datetime import datetime
        
        # 验证路径是否存在
        export_dir = os.path.dirname(export_path)
        if not os.path.exists(export_dir):
            try:
                os.makedirs(export_dir)
                logger.info(f"创建导出目录: {export_dir}")
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'无法创建导出目录: {str(e)}'
                }), 400
        
        # 获取所有lotprioritydone数据
        query = text("""
            SELECT 
                PRIORITY, LOT_ID, DEVICE, CHIP_ID, HANDLER_ID, GOOD_QTY,
                comprehensive_score, STAGE, STEP, WIP_STATE, CREATE_TIME
            FROM lotprioritydone 
            ORDER BY PRIORITY ASC, CREATE_TIME DESC
        """)
        
        result = db.session.execute(query)
        
        # 构建导出数据（与exportData()保持一致的字段名称）
        export_data = []
        for row in result.fetchall():
            export_data.append({
                '优先级': row[0] or '',
                '内部工单号': row[1] or '',
                '产品名称': row[2] or '',
                '芯片名称': row[3] or '',
                '分选机ID': row[4] or '',
                '良品数量': row[5] or 0,
                '综合评分': row[6] or 0.0,
                '工序': row[7] or '',
                '工步': row[8] or '',
                '状态': row[9] or '',
                '创建时间': row[10] or ''
            })
        
        if not export_data:
            return jsonify({
                'success': False,
                'message': '没有数据可以导出'
            }), 400
        
        # 创建DataFrame并导出到Excel
        df = pd.DataFrame(export_data)
        
        # 确保文件扩展名为.xlsx
        if not export_path.lower().endswith('.xlsx'):
            export_path = export_path + '.xlsx'
        
        with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='已排产批次', index=False)
            
            # 调整列宽以适应内容
            worksheet = writer.sheets['已排产批次']
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)  # 最大宽度50
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        logger.info(f"✅ 成功导出 {len(export_data)} 条排产记录到: {export_path}")
        
        return jsonify({
            'success': True,
            'message': f'成功导出 {len(export_data)} 条记录到指定路径',
            'export_path': export_path,
            'records_count': len(export_data)
        })
        
    except Exception as e:
        logger.error(f"❌ 导出排产结果到指定路径失败: {e}")
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500 

# ====== 阶段3.1：统一导出功能 ======

@done_lots_bp.route(get_api_route('production/done-lots/export'), methods=['GET', 'POST'])
@login_required
def export_done_lots_unified():
    """
    基于统一数据服务的导出功能
    支持复杂筛选条件和三种模式的数据导出
    
    参数：
    - mode: 'view', 'adjust', 'final_result' (默认: 'view')
    - export_type: 'filtered', 'all' (默认: 'filtered')
    - format: 'excel', 'csv' (默认: 'excel')
    - 所有筛选参数 (lot_id, device, handler_id等)
    """
    try:
        from app.services.done_lots_data_service import DoneLotsDataService
        import pandas as pd
        from datetime import datetime
        from flask import make_response
        import io
        
        # 获取参数
        if request.method == 'POST':
            params = request.get_json() or {}
        else:
            params = request.args.to_dict()
        
        # 解析参数
        mode = params.get('mode', 'view')
        export_type = params.get('export_type', 'filtered')  # 'filtered' 或 'all'
        format_type = params.get('format', 'excel')  # 'excel' 或 'csv'
        
        logger.info(f"🚀 开始导出数据: mode={mode}, type={export_type}, format={format_type}")
        
        # 创建数据服务实例
        data_service = DoneLotsDataService()
        
        # 准备筛选条件
        filters = {}
        if export_type == 'filtered':
            # 提取筛选参数
            filter_params = ['lot_id', 'device', 'handler_id', 'priority_min', 'priority_max',
                           'quantity_min', 'quantity_max', 'score_min', 'score_max',
                           'stage', 'wip_state', 'date_start', 'date_end', 'search']
            
            for param in filter_params:
                if param in params and params[param]:
                    filters[param] = params[param]
        
        # 根据模式获取数据
        if mode == 'view':
            # 获取成功批次数据 - 不使用分页，获取全量数据
            data = data_service.get_done_lots_data(filters=filters, pagination={'enabled': False})
        elif mode == 'adjust':
            # 调整模式包含成功和失败批次
            success_data = data_service.get_done_lots_data(filters=filters, pagination={'enabled': False})
            failed_data = data_service.get_failed_lots_data(filters=filters)
            # 合并数据
            data = {
                'data': success_data.get('data', []) + failed_data.get('data', []),
                'total': (success_data.get('total', 0) + failed_data.get('total', 0))
            }
        elif mode == 'final_result':
            # 获取最终结果数据
            data = data_service.get_final_result_data(filters=filters, pagination={'enabled': False})
        else:
            return jsonify({
                'success': False,
                'message': f'不支持的模式: {mode}'
            }), 400
        
        if not data.get('data'):
            return jsonify({
                'success': False,
                'message': '没有数据可以导出'
            }), 400
        
        records_count = len(data['data'])
        logger.info(f"📊 准备导出 {records_count} 条记录")
        
        # 准备导出数据
        export_data = []
        for item in data['data']:
            row_data = {
                '优先级': item.get('priority', ''),
                '内部工单号': item.get('lot_id', ''),
                '产品名称': item.get('device', '') or item.get('product_name', ''),
                '芯片名称': item.get('chip_id', ''),
                '分选机ID': item.get('handler_id', ''),
                '数量': item.get('quantity', 0) or item.get('good_qty', 0),
                '综合评分': item.get('score', 0.0) or item.get('comprehensive_score', 0.0),
                '工序': item.get('stage', ''),
                '工步': item.get('step', ''),
                '状态': item.get('status', '') or item.get('wip_state', ''),
                '创建时间': item.get('create_time', '') or item.get('CREATE_TIME', '')
            }
            
            # 根据模式添加特有字段
            if mode == 'adjust':
                row_data['失败原因'] = item.get('failure_reason', '')
                row_data['错误信息'] = item.get('error_message', '')
            elif mode == 'final_result':
                row_data['发布时间'] = item.get('published_time', '')
                row_data['调整次数'] = item.get('adjustment_count', 0)
            
            export_data.append(row_data)
        
        # 创建DataFrame
        df = pd.DataFrame(export_data)
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        mode_name = {'view': '查看模式', 'adjust': '调整模式', 'final_result': '最终结果'}[mode]
        type_name = {'filtered': '筛选结果', 'all': '全部数据'}[export_type]
        filename = f"已排产批次_{mode_name}_{type_name}_{timestamp}"
        
        # 根据格式类型导出
        if format_type == 'csv':
            # CSV导出
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')
            output.seek(0)
            
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}.csv"'
            
        else:
            # Excel导出 (默认)
            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=mode_name, index=False)
                
                # 调整列宽
                worksheet = writer.sheets[mode_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            cell_length = len(str(cell.value))
                            if cell_length > max_length:
                                max_length = cell_length
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            output.seek(0)
            
            response = make_response(output.read())
            response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            response.headers['Content-Disposition'] = f'attachment; filename="{filename}.xlsx"'
        
        logger.info(f"✅ 成功导出 {records_count} 条记录 ({mode_name} - {type_name})")
        return response
        
    except Exception as e:
        logger.error(f"❌ 统一导出功能失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        }), 500

# ====== 阶段3.2：异步导出任务API ======

@done_lots_bp.route(get_api_route('production/done-lots/export-async'), methods=['POST'])
@login_required
def create_export_task():
    """
    创建异步导出任务
    用于大数据量导出的后台处理
    
    参数：
    - mode: 'view', 'adjust', 'final_result'
    - export_type: 'filtered', 'all'
    - format: 'excel', 'csv'
    - 所有筛选参数
    """
    try:
        from app.services.export_task_manager import export_task_manager
        from flask_login import current_user
        
        data = request.get_json() or {}
        
        # 验证必要参数
        mode = data.get('mode', 'view')
        if mode not in ['view', 'adjust', 'final_result']:
            return jsonify({
                'success': False,
                'message': '无效的导出模式'
            }), 400
        
        # 构建导出配置
        export_config = {
            'mode': mode,
            'export_type': data.get('export_type', 'filtered'),
            'format': data.get('format', 'excel'),
            'filters': {},
            'user_id': current_user.id if current_user.is_authenticated else None
        }
        
        # 提取筛选条件
        filter_params = ['lot_id', 'device', 'handler_id', 'priority_min', 'priority_max',
                        'quantity_min', 'quantity_max', 'score_min', 'score_max',
                        'stage', 'wip_state', 'date_start', 'date_end', 'search']
        
        for param in filter_params:
            if param in data and data[param]:
                export_config['filters'][param] = data[param]
        
        # 创建任务
        task_id = export_task_manager.create_export_task(export_config)
        
        logger.info(f"📤 创建异步导出任务: {task_id} - {mode}模式")
        
        return jsonify({
            'success': True,
            'message': '导出任务已创建',
            'task_id': task_id
        })
        
    except Exception as e:
        logger.error(f"❌ 创建导出任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'创建导出任务失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/export-status/<task_id>'), methods=['GET'])
@login_required
def get_export_task_status(task_id: str):
    """获取导出任务状态"""
    try:
        from app.services.export_task_manager import export_task_manager
        
        status = export_task_manager.get_task_status(task_id)
        
        if not status:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f"❌ 获取任务状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取任务状态失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/export-download/<task_id>'), methods=['GET'])
@login_required
def download_export_file(task_id: str):
    """下载导出文件"""
    try:
        from app.services.export_task_manager import export_task_manager
        from flask import send_file
        
        status = export_task_manager.get_task_status(task_id)
        
        if not status:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            }), 404
        
        if status['status'] != 'completed':
            return jsonify({
                'success': False,
                'message': '文件尚未准备好'
            }), 400
        
        if not status['file_path'] or not os.path.exists(status['file_path']):
            return jsonify({
                'success': False,
                'message': '文件不存在或已被清理'
            }), 404
        
        filename = os.path.basename(status['file_path'])
        
        logger.info(f"📥 下载导出文件: {task_id} - {filename}")
        
        return send_file(
            status['file_path'],
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        logger.error(f"❌ 下载导出文件失败: {e}")
        return jsonify({
            'success': False,
            'message': f'下载失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/export-cancel/<task_id>'), methods=['POST'])
@login_required
def cancel_export_task(task_id: str):
    """取消导出任务"""
    try:
        from app.services.export_task_manager import export_task_manager
        
        success = export_task_manager.cancel_task(task_id)
        
        if not success:
            return jsonify({
                'success': False,
                'message': '无法取消任务（任务不存在或已完成）'
            }), 400
        
        logger.info(f"❌ 导出任务已取消: {task_id}")
        
        return jsonify({
            'success': True,
            'message': '任务已取消'
        })
        
    except Exception as e:
        logger.error(f"❌ 取消导出任务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'取消任务失败: {str(e)}'
        }), 500

@done_lots_bp.route(get_api_route('production/done-lots/export-tasks'), methods=['GET'])
@login_required
def list_export_tasks():
    """获取导出任务列表"""
    try:
        from app.services.export_task_manager import export_task_manager
        
        limit = int(request.args.get('limit', 20))
        tasks = export_task_manager.get_all_tasks(limit=limit)
        
        return jsonify({
            'success': True,
            'data': tasks,
            'total': len(tasks)
        })
        
    except Exception as e:
        logger.error(f"❌ 获取任务列表失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取任务列表失败: {str(e)}'
        }), 500

def get_failed_lots_from_logs_cached():
    """失败批次获取的缓存版本 - 使用统一缓存系统"""
    try:
        import time
        from app.utils.db_connection_pool import get_db_connection_context
        
        # 🔥 获取查询参数
        current_only = request.args.get('current_only', 'false').lower() == 'true'
        hours_limit = int(request.args.get('hours', '24'))  # 默认24小时内的失败记录
        
        logger.info(f"🔍 缓存版本：获取失败批次数据 (current_only={current_only}, hours_limit={hours_limit})")
        
        # 🚀 使用缓存键
        cache_key = f"failed_lots_{current_only}_{hours_limit}"
        
        def fetch_failed_lots_data():
            """实际获取失败批次数据的函数"""
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    
                    # 检查表是否存在
                    check_table_sql = """
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'scheduling_failed_lots'
                    """
                    cursor.execute(check_table_sql)
                    check_result = cursor.fetchone()
                    
                    if isinstance(check_result, dict):
                        table_exists = list(check_result.values())[0] > 0
                    else:
                        table_exists = check_result[0] > 0
                    
                    if not table_exists:
                        return {
                            'success': False,
                            'error': 'scheduling_failed_lots表不存在'
                        }
                    
                    # 🔧 优化的SQL查询（移除字符集转换）
                    if current_only:
                        query_sql = """
                        SELECT
                            sfl.lot_id,
                            sfl.device,
                            sfl.stage,
                            sfl.good_qty,
                            sfl.failure_reason,
                            sfl.failure_details,
                            sfl.suggestion,
                            sfl.session_id,
                            sfl.timestamp,
                            -- 从ET_WAIT_LOT表获取关键字段（移除字符集转换）
                            ewl.LOT_TYPE,
                            ewl.PKG_PN
                        FROM scheduling_failed_lots sfl
                        LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                        WHERE sfl.timestamp >= DATE_SUB(NOW(), INTERVAL %s HOUR)
                        ORDER BY sfl.timestamp DESC
                        LIMIT 1000
                        """
                        cursor.execute(query_sql, (hours_limit,))
                    else:
                        query_sql = """
                        SELECT
                            sfl.lot_id,
                            sfl.device,
                            sfl.stage,
                            sfl.good_qty,
                            sfl.failure_reason,
                            sfl.failure_details,
                            sfl.suggestion,
                            sfl.session_id,
                            sfl.timestamp,
                            -- 从ET_WAIT_LOT表获取关键字段（移除字符集转换）
                            ewl.LOT_TYPE,
                            ewl.PKG_PN
                        FROM scheduling_failed_lots sfl
                        LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                        ORDER BY sfl.timestamp DESC
                        LIMIT 1000
                        """
                        cursor.execute(query_sql)
                    
                    results = cursor.fetchall()
                    
                    if not results:
                        return {
                            'success': True,
                            'data': {
                                'failed_lots': [],
                                'total_count': 0,
                                'summary': {
                                    'total_failed': 0,
                                    'config_missing': 0,
                                    'equipment_incompatible': 0,
                                    'other_reasons': 0
                                }
                            },
                            'message': '暂无失败批次数据'
                        }
                    
                    # 处理查询结果
                    failed_lots = []
                    summary_stats = {
                        'total_failed': 0,
                        'config_missing': 0,
                        'equipment_incompatible': 0,
                        'other_reasons': 0
                    }
                    
                    for row in results:
                        lot_id = row['lot_id']
                        device = row['device'] or ''
                        stage = row['stage'] or ''
                        good_qty = row['good_qty'] or 0
                        failure_reason = row['failure_reason'] or '未知原因'
                        failure_details = row['failure_details'] or ''
                        suggestion = row.get('suggestion', '') or generate_suggestion(failure_reason, failure_details, device, stage)
                        session_id = row.get('session_id', '')
                        timestamp = row['timestamp']

                        # 获取ET_WAIT_LOT表的关键信息
                        lot_type = row.get('LOT_TYPE', '') or ''
                        pkg_pn = row.get('PKG_PN', '') or ''
                        
                        # 统计失败原因
                        summary_stats['total_failed'] += 1
                        if '配置' in failure_reason or 'config' in failure_reason.lower():
                            summary_stats['config_missing'] += 1
                        elif '设备' in failure_reason or '不兼容' in failure_reason:
                            summary_stats['equipment_incompatible'] += 1
                        else:
                            summary_stats['other_reasons'] += 1
                        
                        failure_info = {
                            'LOT_ID': lot_id,
                            'DEVICE': device,
                            'STAGE': stage,
                            'LOT_TYPE': lot_type,
                            'PKG_PN': pkg_pn,
                            'GOOD_QTY': good_qty,
                            'failure_reason': failure_reason,
                            'suggestion': suggestion,
                            'timestamp': timestamp.isoformat() if timestamp else datetime.now().isoformat(),
                            'session_id': session_id
                        }
                        failed_lots.append(failure_info)
                    
                    cursor.close()
                    
                    return {
                        'success': True,
                        'data': {
                            'failed_lots': failed_lots,
                            'total_count': len(failed_lots),
                            'summary': summary_stats
                        },
                        'debug_info': {
                            'data_source': 'database_cached',
                            'table_name': 'scheduling_failed_lots',
                            'total_records': len(failed_lots),
                            'record_count': len(failed_lots),  # 兼容前端
                            'cache_key': cache_key
                        }
                    }
                    
            except Exception as e:
                logger.error(f"❌ 缓存版本获取失败批次数据失败: {e}")
                return {
                    'success': False,
                    'error': str(e)
                }
        
        # 🚀 使用缓存系统
        try:
            # 设置缓存时间：3分钟
            cached_result = cache_get(cache_key)
            if cached_result is not None:
                logger.info(f"✅ 缓存命中: {cache_key}")
                return jsonify(cached_result)
            
            # 缓存未命中，获取新数据
            result = fetch_failed_lots_data()
            if result.get('success'):
                cache_set(cache_key, result, 180)  # 3分钟缓存
                logger.info(f"✅ 缓存已更新: {cache_key}")
            
            return jsonify(result)
            
        except Exception as cache_error:
            logger.warning(f"⚠️ 缓存操作失败，使用直接查询: {cache_error}")
            return jsonify(fetch_failed_lots_data())
            
    except Exception as e:
        logger.error(f"❌ 缓存版本API失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'data': {
                'failed_lots': [],
                'total_count': 0,
                'summary': {
                    'total_failed': 0,
                    'config_missing': 0,
                    'equipment_incompatible': 0,
                    'other_reasons': 0
                }
            }
        })

def invalidate_failed_lots_cache():
    """失效失败批次相关的所有缓存"""
    try:
        from app.utils.simple_cache import cache_delete
        
        # 清理所有相关缓存键
        cache_keys = [
            'failed_lots_True_24',
            'failed_lots_False_24',
            'failed_lots_True_168',  # 7天
            'failed_lots_False_168'
        ]
        
        cleared_count = 0
        for key in cache_keys:
            try:
                cache_delete(key)
                cleared_count += 1
            except:
                pass
        
        logger.info(f"🧹 已清理 {cleared_count} 个失败批次缓存键")
        
        return jsonify({
            'success': True,
            'message': f'已清理 {cleared_count} 个缓存键',
            'cleared_keys': cleared_count
        })
        
    except Exception as e:
        logger.error(f"❌ 缓存清理失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })


@done_lots_bp.route(get_api_route('production/get-latest-scheduling-session'), methods=['GET'])
def get_latest_scheduling_session():
    """获取最新的调度会话时间，用于failed-lots页面的自动刷新监控"""
    try:
        from app.utils.db_connection_pool import get_db_connection_context
        
        logger.info("🔍 查询最新调度会话时间...")
        
        with get_db_connection_context() as conn:
            cursor = conn.cursor()
            
            # 查询最新的失败批次时间戳作为调度会话时间
            query_sql = """
            SELECT MAX(timestamp) as latest_session_time
            FROM scheduling_failed_lots
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 DAY)
            """
            
            cursor.execute(query_sql)
            result = cursor.fetchone()
            
            if isinstance(result, dict):
                latest_time = result.get('latest_session_time')
            else:
                latest_time = result[0] if result else None
            
            # 格式化时间
            if latest_time:
                if isinstance(latest_time, datetime):
                    latest_time_str = latest_time.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    latest_time_str = str(latest_time)
            else:
                latest_time_str = None
            
            return jsonify({
                'success': True,
                'data': {
                    'latest_session_time': latest_time_str,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                'message': '最新调度会话时间获取成功'
            })
            
    except Exception as e:
        logger.error(f"❌ 获取最新调度会话时间失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'message': '获取最新调度会话时间失败'
        }), 500