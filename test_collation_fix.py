#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试字符集冲突修复
"""

# 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('CollationTest')

def test_collation_fix():
    """测试字符集冲突修复"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 测试有问题的查询
                logger.info("🧪 测试修复后的JOIN查询...")
                
                test_query = """
                SELECT
                    sfl.lot_id,
                    sfl.device,
                    sfl.stage,
                    sfl.failure_reason,
                    ewl.LOT_TYPE,
                    ewl.PKG_PN
                FROM scheduling_failed_lots sfl
                LEFT JOIN et_wait_lot ewl ON CAST(sfl.lot_id AS CHAR) = CAST(ewl.LOT_ID AS CHAR)
                LIMIT 3
                """
                
                cursor.execute(test_query)
                results = cursor.fetchall()
                
                logger.info(f"✅ 修复后的查询成功执行，返回 {len(results)} 条记录")
                
                if results:
                    for i, row in enumerate(results, 1):
                        logger.info(f"  记录{i}: lot_id={row.get('lot_id')}, LOT_TYPE={row.get('LOT_TYPE')}")
                
                # 测试原有问题查询（应该会失败）
                logger.info("🧪 测试原有问题查询（用于对比）...")
                try:
                    problem_query = """
                    SELECT
                        sfl.lot_id,
                        ewl.LOT_TYPE
                    FROM scheduling_failed_lots sfl
                    LEFT JOIN et_wait_lot ewl ON sfl.lot_id = ewl.LOT_ID
                    LIMIT 1
                    """
                    
                    cursor.execute(problem_query)
                    cursor.fetchall()
                    logger.warning("⚠️ 原有查询竟然也成功了，可能数据库设置已修改")
                    
                except Exception as e:
                    logger.info(f"❌ 原有查询失败（预期的）: {e}")
                
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_api_directly():
    """直接调用API函数测试"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            # 模拟请求上下文
            with app.test_request_context('/?current_only=false'):
                from app.api_v2.production.done_lots_api import get_failed_lots_from_logs_cached
                
                logger.info("🧪 直接调用缓存API函数...")
                
                response = get_failed_lots_from_logs_cached()
                
                # 解析响应
                if hasattr(response, 'get_json'):
                    result = response.get_json()
                elif hasattr(response, 'data'):
                    import json
                    result = json.loads(response.data)
                else:
                    result = response
                
                logger.info(f"✅ API直接调用成功: {result.get('success', False)}")
                
                if result.get('success'):
                    data = result.get('data', {})
                    failed_lots = data.get('failed_lots', [])
                    logger.info(f"  获取到 {len(failed_lots)} 条失败批次记录")
                else:
                    error = result.get('error', 'Unknown error')
                    logger.error(f"  API返回错误: {error}")
                
                return result.get('success', False)
                
    except Exception as e:
        logger.error(f"❌ API直接调用失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("🔧 开始字符集冲突修复测试...")
    
    # 测试1: 直接数据库查询
    logger.info("📋 测试1: 直接数据库查询")
    test1_success = test_collation_fix()
    
    # 测试2: API函数直接调用
    logger.info("📋 测试2: API函数直接调用")
    test2_success = test_api_directly()
    
    if test1_success and test2_success:
        logger.info("🎉 所有测试通过！字符集冲突已修复")
        return True
    else:
        logger.error("❌ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")