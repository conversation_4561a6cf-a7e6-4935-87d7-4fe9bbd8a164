#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法权重配置检查工具 - 调查90%权重问题
"""

import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

import os
import logging
from datetime import datetime

os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('weight_config_check.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('WeightConfigChecker')

def check_weight_configuration():
    """检查算法权重配置"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            from app.models import SchedulingConfig
            from sqlalchemy import text
            
            # 检查数据库中的权重配置
            logger.info("🔍 检查数据库中的权重配置记录...")
            configs = SchedulingConfig.query.all()
            
            if not configs:
                logger.warning("⚠️  数据库中没有权重配置记录，使用默认值")
                logger.info("📊 默认权重配置:")
                logger.info("   - 技术匹配权重: 25.00%")
                logger.info("   - 负载均衡权重: 20.00%") 
                logger.info("   - 截止时间权重: 25.00%")
                logger.info("   - 价值效率权重: 20.00%")
                logger.info("   - 业务优先级权重: 10.00%")
                logger.info(f"   - 总和: {25+20+25+20+10}%")
                
                # 创建默认配置记录
                default_config = SchedulingConfig(
                    tech_match_weight=25.00,
                    load_balance_weight=20.00,
                    deadline_weight=25.00,
                    value_efficiency_weight=20.00,
                    business_priority_weight=10.00,
                    is_active=True,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                from app import db
                try:
                    db.session.add(default_config)
                    db.session.commit()
                    logger.info("✅ 创建默认权重配置记录")
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"❌ 创建默认配置失败: {e}")
                    
            else:
                logger.info(f"📊 发现 {len(configs)} 个权重配置记录:")
                for i, config in enumerate(configs):
                    logger.info(f"\n--- 配置记录 #{i+1} (ID: {config.id}) ---")
                    logger.info(f"   - 技术匹配权重: {config.tech_match_weight}%")
                    logger.info(f"   - 负载均衡权重: {config.load_balance_weight}%")
                    logger.info(f"   - 截止时间权重: {config.deadline_weight}%")
                    logger.info(f"   - 价值效率权重: {config.value_efficiency_weight}%")
                    logger.info(f"   - 业务优先级权重: {config.business_priority_weight}%")
                    
                    total = (config.tech_match_weight + 
                            config.load_balance_weight + 
                            config.deadline_weight + 
                            config.value_efficiency_weight + 
                            config.business_priority_weight)
                    
                    logger.info(f"   - 权重总和: {total}%")
                    logger.info(f"   - 是否活跃: {config.is_active}")
                    logger.info(f"   - 创建时间: {config.created_at}")
                    logger.info(f"   - 更新时间: {config.updated_at}")
                    
                    if total != 100:
                        logger.warning(f"🚨 权重总和异常: {total}% (期望100%)")
                        
                        # 修复权重配置到100%
                        if config.is_active:
                            logger.info("🔧 正在修复权重配置...")
                            
                            # 重新分配权重确保总和为100%
                            config.tech_match_weight = 25.00
                            config.load_balance_weight = 25.00  # 增加5%
                            config.deadline_weight = 25.00
                            config.value_efficiency_weight = 15.00  # 减少5%
                            config.business_priority_weight = 10.00
                            config.updated_at = datetime.now()
                            
                            from app import db
                            try:
                                db.session.merge(config)
                                db.session.commit()
                                
                                new_total = 25 + 25 + 25 + 15 + 10
                                logger.info(f"✅ 权重配置已修复: {new_total}%")
                                logger.info("   新配置: 技术25% + 负载25% + 截止25% + 价值15% + 业务10%")
                                
                            except Exception as e:
                                db.session.rollback()
                                logger.error(f"❌ 权重配置修复失败: {e}")
                    else:
                        logger.info("✅ 权重配置正常")
            
            # 验证修复结果
            logger.info("\n🔄 验证修复结果...")
            updated_configs = SchedulingConfig.query.filter_by(is_active=True).all()
            for config in updated_configs:
                total = (config.tech_match_weight + 
                        config.load_balance_weight + 
                        config.deadline_weight + 
                        config.value_efficiency_weight + 
                        config.business_priority_weight)
                logger.info(f"✅ 活跃配置权重总和: {total}%")
                
            return True
            
    except Exception as e:
        logger.error(f"❌ 权重配置检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = check_weight_configuration()
    print("🎉 权重配置检查: 通过" if success else "❌ 权重配置检查: 失败")

if __name__ == "__main__":
    main()