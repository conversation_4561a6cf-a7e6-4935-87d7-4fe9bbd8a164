#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阶段3.1 - 导出功能测试
测试基于统一数据服务的新导出API
"""

# 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 基础导入
import os
import logging
import json
import requests
from datetime import datetime

# 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_export.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestExport')

def test_export_api():
    """测试导出API功能"""
    try:
        # Flask应用创建
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入数据服务进行测试
            from app.services.done_lots_data_service import DoneLotsDataService
            
            # 创建数据服务实例
            data_service = DoneLotsDataService()
            logger.info("✅ 数据服务创建成功")
            
            # 测试1: 基础数据查询
            logger.info("📊 测试1: 查看模式数据查询")
            view_data = data_service.get_done_lots_data(
                pagination={'page': 1, 'size': 5}
            )
            logger.info(f"查看模式数据: {view_data.get('total', 0)}条记录")
            
            # 测试2: 失败批次数据
            logger.info("📊 测试2: 失败批次数据查询") 
            failed_data = data_service.get_failed_lots_data()
            logger.info(f"失败批次数据: {len(failed_data.get('data', []))}条记录")
            
            # 测试3: 统计数据
            logger.info("📊 测试3: 统计数据查询")
            stats = data_service.get_data_statistics()
            logger.info(f"统计数据: {stats}")
            
            # 测试4: 模拟导出API调用
            logger.info("📤 测试4: 模拟导出功能")
            
            # 测试不同模式的导出
            export_tests = [
                {'mode': 'view', 'export_type': 'filtered', 'format': 'excel'},
                {'mode': 'adjust', 'export_type': 'all', 'format': 'csv'},
                {'mode': 'final_result', 'export_type': 'filtered', 'format': 'excel'}
            ]
            
            for test_config in export_tests:
                logger.info(f"测试导出配置: {test_config}")
                
                # 准备筛选条件
                filters = {
                    'search': 'test',  # 测试搜索
                    'priority_min': 50,  # 优先级筛选
                } if test_config['export_type'] == 'filtered' else {}
                
                # 根据模式获取数据
                if test_config['mode'] == 'view':
                    data = data_service.get_done_lots_data(
                        filters=filters, 
                        pagination={'enabled': False}
                    )
                elif test_config['mode'] == 'adjust':
                    success_data = data_service.get_done_lots_data(
                        filters=filters, 
                        pagination={'enabled': False}
                    )
                    failed_data = data_service.get_failed_lots_data(filters=filters)
                    data = {
                        'data': success_data.get('data', []) + failed_data.get('data', []),
                        'total': success_data.get('total', 0) + failed_data.get('total', 0)
                    }
                elif test_config['mode'] == 'final_result':
                    data = data_service.get_final_result_data(
                        filters=filters, 
                        pagination={'enabled': False}
                    )
                
                logger.info(f"✅ {test_config['mode']}模式获取到 {len(data.get('data', []))} 条记录")
                
                # 模拟数据格式化（导出逻辑）
                export_data = []
                for item in data.get('data', [])[:3]:  # 只处理前3条作为示例
                    row_data = {
                        '优先级': item.get('priority', ''),
                        '内部工单号': item.get('lot_id', ''),
                        '产品名称': item.get('device', '') or item.get('product_name', ''),
                        '数量': item.get('quantity', 0) or item.get('good_qty', 0),
                        '创建时间': item.get('create_time', '') or item.get('CREATE_TIME', '')
                    }
                    export_data.append(row_data)
                
                logger.info(f"✅ 格式化完成，准备导出 {len(export_data)} 条记录")
            
            logger.info("🎉 所有导出功能测试通过")
            return True
            
    except Exception as e:
        logger.error(f"❌ 导出功能测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_api_endpoints():
    """测试API端点是否正常工作"""
    try:
        logger.info("🌐 测试API端点（需要Flask应用运行）")
        
        base_url = "http://localhost:5000"
        
        # 测试数据查询端点
        endpoints_to_test = [
            "/api/v2/production/done-lots?page=1&size=5",
            "/api/v2/production/done-lots/failed",
            "/api/v2/production/done-lots/final-result"
        ]
        
        for endpoint in endpoints_to_test:
            try:
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
                logger.info(f"✅ {endpoint}: 状态码 {response.status_code}")
            except requests.exceptions.ConnectionError:
                logger.warning(f"⚠️ {endpoint}: 连接失败（Flask应用未运行）")
            except Exception as e:
                logger.error(f"❌ {endpoint}: {e}")
        
        # 测试导出端点（POST请求）
        export_endpoint = "/api/v2/production/done-lots/export"
        test_payload = {
            "mode": "view",
            "export_type": "filtered", 
            "format": "excel",
            "search": "test"
        }
        
        try:
            response = requests.post(
                f"{base_url}{export_endpoint}",
                json=test_payload,
                timeout=10
            )
            if response.status_code == 200:
                logger.info(f"✅ 导出API测试成功: {len(response.content)} 字节")
            else:
                logger.warning(f"⚠️ 导出API返回状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            logger.warning("⚠️ 导出API测试失败: Flask应用未运行")
        except Exception as e:
            logger.error(f"❌ 导出API测试失败: {e}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ API端点测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始阶段3.1导出功能测试...")
    
    success = True
    
    # 测试1: 数据服务和导出逻辑
    logger.info("=" * 60)
    logger.info("测试1: 数据服务和导出逻辑")
    if not test_export_api():
        success = False
    
    # 测试2: API端点
    logger.info("=" * 60) 
    logger.info("测试2: API端点测试")
    if not test_api_endpoints():
        success = False
    
    # 总结
    logger.info("=" * 60)
    if success:
        logger.info("🎉 阶段3.1导出功能测试: 全部通过")
    else:
        logger.error("❌ 阶段3.1导出功能测试: 部分失败")
    
    return success

if __name__ == "__main__":
    success = main()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")