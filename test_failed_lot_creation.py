#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试失败批次记录创建 - 验证SchedulingFailureTracker功能
测试排产服务中失败记录写入数据库的完整流程

使用标准化测试脚本模板，确保Flask上下文正确管理
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import json

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_failed_lot_creation.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestFailedLotCreation')

def test_failure_tracker():
    """测试SchedulingFailureTracker功能"""
    try:
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用上下文创建成功")
            
            # 导入失败跟踪器
            from scheduling_failure_fix import SchedulingFailureTracker
            
            # 创建失败跟踪器实例
            tracker = SchedulingFailureTracker()
            logger.info("✅ SchedulingFailureTracker创建成功")
            
            # 准备测试数据
            test_lot = {
                'LOT_ID': 'TEST_LOT_001',
                'DEVICE': 'TestDevice_A',
                'STAGE': 'FT',
                'GOOD_QTY': 1500,
                'PKG_PN': 'TEST_PKG',
                'CHIP_ID': 'CHIP_001'
            }
            
            # 测试1: 添加配置缺失的失败记录
            logger.info("🧪 测试1: 配置缺失的失败记录")
            tracker.add_failed_lot(
                lot=test_lot,
                failure_reason="配置需求获取失败",
                failure_details="未找到匹配的测试规范 (DEVICE=TestDevice_A, STAGE=FT)",
                algorithm_version="v2.0",
                session_id="TEST_SESSION_001"
            )
            logger.info("✅ 配置缺失失败记录添加成功")
            
            # 测试2: 添加设备不兼容的失败记录  
            test_lot['LOT_ID'] = 'TEST_LOT_002'
            test_lot['DEVICE'] = 'TestDevice_B'
            logger.info("🧪 测试2: 设备不兼容的失败记录")
            tracker.add_failed_lot(
                lot=test_lot,
                failure_reason="无合适设备",
                failure_details="所有设备都不兼容当前批次的测试需求",
                algorithm_version="v2.0",
                session_id="TEST_SESSION_001"
            )
            logger.info("✅ 设备不兼容失败记录添加成功")
            
            # 测试3: 添加算法异常的失败记录
            test_lot['LOT_ID'] = 'TEST_LOT_003'
            test_lot['DEVICE'] = 'TestDevice_C'
            logger.info("🧪 测试3: 算法异常的失败记录")
            tracker.add_failed_lot(
                lot=test_lot,
                failure_reason="算法执行异常",
                failure_details="排产算法在处理批次时出现意外错误",
                algorithm_version="v2.0", 
                session_id="TEST_SESSION_002"
            )
            logger.info("✅ 算法异常失败记录添加成功")
            
            # 保存失败记录到数据库
            logger.info("💾 保存失败记录到数据库...")
            success_count = tracker.save_to_database()
            logger.info(f"✅ 成功保存 {success_count} 条失败记录到数据库")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 失败跟踪器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def verify_database_records():
    """验证数据库中的失败记录"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🔍 验证数据库中的失败记录...")
                
                # 查询刚才插入的测试记录
                query_sql = """
                SELECT lot_id, device, stage, failure_reason, suggestion, session_id
                FROM scheduling_failed_lots 
                WHERE lot_id LIKE 'TEST_LOT_%' 
                ORDER BY timestamp DESC
                """
                
                cursor.execute(query_sql)
                records = cursor.fetchall()
                
                logger.info(f"📊 找到 {len(records)} 条测试失败记录:")
                for record in records:
                    if isinstance(record, dict):
                        logger.info(f"   - {record['lot_id']}: {record['device']} | {record['failure_reason']}")
                        logger.info(f"     建议: {record['suggestion'][:50]}..." if record['suggestion'] else "     建议: 无")
                    else:
                        logger.info(f"   - {record[0]}: {record[1]} | {record[3]}")
                        logger.info(f"     建议: {record[4][:50]}..." if record[4] else "     建议: 无")
                
                # 统计不同失败类型
                stats_sql = """
                SELECT failure_reason, COUNT(*) as count
                FROM scheduling_failed_lots 
                WHERE lot_id LIKE 'TEST_LOT_%'
                GROUP BY failure_reason
                """
                
                cursor.execute(stats_sql)
                stats = cursor.fetchall()
                
                logger.info("📈 失败类型统计:")
                for stat in stats:
                    if isinstance(stat, dict):
                        logger.info(f"   - {stat['failure_reason']}: {stat['count']} 条")
                    else:
                        logger.info(f"   - {stat[0]}: {stat[1]} 条")
                
                cursor.close()
                return len(records) > 0
                
    except Exception as e:
        logger.error(f"❌ 数据库记录验证失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_api_integration():
    """测试API集成 - 验证API能正确读取失败记录"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("🌐 测试API集成...")
            
            # 模拟API调用
            from app.api_v2.production.done_lots_api import get_failed_lots_from_logs
            
            # 创建模拟请求
            class MockRequest:
                def __init__(self):
                    self.args = {'current_only': 'false', 'hours': '24'}
            
            # 替换Flask request对象进行测试
            import app.api_v2.production.done_lots_api as api_module
            original_request = getattr(api_module, 'request', None)
            api_module.request = MockRequest()
            
            try:
                # 调用API函数
                response = get_failed_lots_from_logs()
                
                if hasattr(response, 'get_json'):
                    result = response.get_json()
                else:
                    result = response[0].get_json()  # 如果返回的是tuple
                
                if result and result.get('success'):
                    failed_lots = result.get('data', {}).get('failed_lots', [])
                    logger.info(f"✅ API返回 {len(failed_lots)} 条失败记录")
                    
                    # 检查测试数据是否在API结果中
                    test_lots_found = [lot for lot in failed_lots if lot.get('LOT_ID', '').startswith('TEST_LOT_')]
                    logger.info(f"🎯 找到 {len(test_lots_found)} 条测试失败记录")
                    
                    return len(test_lots_found) > 0
                else:
                    logger.error(f"❌ API调用失败: {result}")
                    return False
                    
            finally:
                # 恢复原始request对象
                if original_request:
                    api_module.request = original_request
                
    except Exception as e:
        logger.error(f"❌ API集成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def cleanup_test_data():
    """清理测试数据"""
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.utils.db_connection_pool import get_db_connection_context
            
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                logger.info("🧹 清理测试数据...")
                
                delete_sql = "DELETE FROM scheduling_failed_lots WHERE lot_id LIKE 'TEST_LOT_%'"
                cursor.execute(delete_sql)
                deleted_count = cursor.rowcount
                
                logger.info(f"✅ 清理了 {deleted_count} 条测试数据")
                cursor.close()
                return True
                
    except Exception as e:
        logger.error(f"❌ 测试数据清理失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始测试失败批次记录创建功能...")
    
    # 测试1: 失败跟踪器功能
    logger.info("=" * 50)
    logger.info("阶段1: 测试SchedulingFailureTracker")
    success1 = test_failure_tracker()
    if not success1:
        logger.error("❌ 失败跟踪器测试失败")
        return False
    
    # 测试2: 验证数据库记录
    logger.info("=" * 50)
    logger.info("阶段2: 验证数据库记录")
    success2 = verify_database_records()
    if not success2:
        logger.error("❌ 数据库记录验证失败")
        return False
    
    # 测试3: API集成测试
    logger.info("=" * 50)
    logger.info("阶段3: 测试API集成")
    success3 = test_api_integration()
    if not success3:
        logger.error("❌ API集成测试失败")
        return False
    
    # 测试4: 清理测试数据
    logger.info("=" * 50)
    logger.info("阶段4: 清理测试数据")
    success4 = cleanup_test_data()
    if not success4:
        logger.error("❌ 测试数据清理失败")
        return False
    
    logger.info("🎉 所有测试完成!")
    return True

if __name__ == "__main__":
    success = main()
    print("🎉 失败记录创建测试: 成功" if success else "❌ 失败记录创建测试: 失败")