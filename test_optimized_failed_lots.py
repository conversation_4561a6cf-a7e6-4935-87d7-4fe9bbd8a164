#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的failed-lots页面逻辑
"""

# 1. 编码修复
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import requests
import time

# 3. 路径设置
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_optimized_failed_lots.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestOptimizedFailedLots')

def test_optimized_failed_lots():
    """测试优化后的failed-lots逻辑"""
    try:
        base_url = "http://localhost:5000"
        
        print("🧪 测试优化后的failed-lots页面逻辑...")
        
        # 测试1: 测试最新调度会话模式（current_only=true）
        print("\n1. 测试最新调度会话模式...")
        current_url = f"{base_url}/api/v2/production/get-failed-lots-from-logs?current_only=true"
        cache_bust = int(time.time())
        current_url_with_cache_bust = f"{current_url}&_cache_bust={cache_bust}"
        
        print(f"请求URL: {current_url_with_cache_bust}")
        
        response = requests.get(current_url_with_cache_bust, timeout=10)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"API响应成功: {data.get('success', False)}")
            
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                total_failed = data.get('data', {}).get('summary', {}).get('total_failed', 0)
                debug_info = data.get('debug_info', {})
                
                print(f"✅ 最新调度会话失败批次: {len(failed_lots)}条")
                print(f"📊 统计总数: {total_failed}")
                print(f"🔍 查询模式: {debug_info.get('query_mode', 'unknown')}")
                
                # 显示最近几条记录的会话ID
                if failed_lots:
                    print(f"\n最近3条失败记录的会话信息:")
                    for i, lot in enumerate(failed_lots[:3]):
                        lot_id = lot.get('LOT_ID', 'unknown')
                        session_id = lot.get('session_id', 'unknown')
                        timestamp = lot.get('timestamp', 'unknown')
                        print(f"  {i+1}. {lot_id} | 会话: {session_id} | 时间: {timestamp}")
                
            else:
                print(f"❌ API返回失败: {data.get('message', 'unknown error')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
        
        # 测试2: 测试所有历史模式（current_only=false）
        print("\n2. 测试所有历史模式...")
        history_url = f"{base_url}/api/v2/production/get-failed-lots-from-logs?current_only=false"
        history_url_with_cache_bust = f"{history_url}&_cache_bust={cache_bust}"
        
        print(f"请求URL: {history_url_with_cache_bust}")
        
        response = requests.get(history_url_with_cache_bust, timeout=15)
        print(f"响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                failed_lots = data.get('data', {}).get('failed_lots', [])
                total_failed = data.get('data', {}).get('summary', {}).get('total_failed', 0)
                debug_info = data.get('debug_info', {})
                
                print(f"✅ 所有历史失败批次: {len(failed_lots)}条")
                print(f"📊 统计总数: {total_failed}")
                print(f"🔍 查询模式: {debug_info.get('query_mode', 'unknown')}")
                
                # 统计不同会话ID的数量
                if failed_lots:
                    session_ids = set()
                    for lot in failed_lots:
                        session_id = lot.get('session_id', '')
                        if session_id:
                            session_ids.add(session_id)
                    
                    print(f"📊 涉及的调度会话数量: {len(session_ids)}")
                    print(f"🔗 会话ID列表: {list(session_ids)[:5]}...")  # 显示前5个
        
        # 测试3: 数据库验证
        print("\n3. 数据库验证...")
        try:
            from app import create_app
            app, socketio = create_app()
            
            with app.app_context():
                from app.utils.db_connection_pool import get_db_connection_context
                
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    
                    # 获取最新会话ID
                    cursor.execute("""
                        SELECT session_id, COUNT(*) as count, MIN(timestamp) as first_time, MAX(timestamp) as last_time
                        FROM scheduling_failed_lots 
                        WHERE session_id IS NOT NULL AND session_id != ''
                        GROUP BY session_id 
                        ORDER BY MAX(timestamp) DESC 
                        LIMIT 5
                    """)
                    
                    session_stats = cursor.fetchall()
                    print(f"\n最新5个调度会话统计:")
                    for i, stats in enumerate(session_stats):
                        if isinstance(stats, dict):
                            session_id = stats.get('session_id', 'unknown')
                            count = stats.get('count', 0)
                            first_time = stats.get('first_time', 'unknown')
                            last_time = stats.get('last_time', 'unknown')
                        else:
                            session_id = stats[0]
                            count = stats[1]
                            first_time = stats[2]
                            last_time = stats[3]
                        
                        print(f"  {i+1}. 会话: {session_id}")
                        print(f"     失败批次数: {count}")
                        print(f"     时间范围: {first_time} ~ {last_time}")
                        print()
                    
        except Exception as db_error:
            print(f"❌ 数据库验证失败: {db_error}")
        
        print("🎉 优化后逻辑测试完成!")
        print("\n✅ 主要改进验证:")
        print("  1. 🎯 当前失败批次基于最新调度会话，不再依赖时间范围")
        print("  2. 📊 历史记录显示所有会话的失败批次")
        print("  3. 🔍 API返回的debug_info包含query_mode信息")
        print("  4. 🏷️ 每个失败批次都包含session_id信息")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_optimized_failed_lots()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")

if __name__ == "__main__":
    main()