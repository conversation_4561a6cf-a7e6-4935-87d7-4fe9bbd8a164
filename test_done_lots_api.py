#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已排产表API接口重构
验证API接口与数据服务的集成
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
import json
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_done_lots_api.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestDoneLotsAPI')

def test_done_lots_api():
    """测试已排产表API接口"""
    try:
        logger.info("🧪 开始测试已排产表API接口...")
        
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            with app.test_client() as client:
                logger.info("✅ Flask应用和测试客户端创建成功")
                
                # 测试1: 基础数据查询（默认参数）
                logger.info("🧪 测试1: 基础数据查询（默认参数）")
                response1 = client.get('/api/v2/production/done-lots?table=lotprioritydone')
                
                if response1.status_code == 200:
                    data1 = response1.get_json()
                    if data1.get('success'):
                        logger.info(f"✅ 基础查询成功: {len(data1['data'])}条记录，总数: {data1['pagination']['total']}")
                        if data1['data']:
                            sample = data1['data'][0]
                            logger.info(f"📊 示例记录: LOT_ID={sample.get('LOT_ID')}, DEVICE={sample.get('DEVICE')}")
                    else:
                        logger.error(f"❌ 基础查询失败: {data1.get('message')}")
                else:
                    logger.error(f"❌ 基础查询HTTP错误: {response1.status_code}")
                
                # 测试2: 分页查询
                logger.info("🧪 测试2: 分页查询（第2页，每页10条）")
                response2 = client.get('/api/v2/production/done-lots?table=lotprioritydone&page=2&size=10')
                
                if response2.status_code == 200:
                    data2 = response2.get_json()
                    if data2.get('success'):
                        logger.info(f"✅ 分页查询成功: 第{data2['pagination']['page']}页，{len(data2['data'])}条记录")
                        logger.info(f"📊 分页信息: 总数={data2['pagination']['total']}, 总页数={data2['pagination']['total_pages']}")
                    else:
                        logger.error(f"❌ 分页查询失败: {data2.get('message')}")
                else:
                    logger.error(f"❌ 分页查询HTTP错误: {response2.status_code}")
                
                # 测试3: 搜索查询
                logger.info("🧪 测试3: 搜索查询（搜索关键字'L'）")
                response3 = client.get('/api/v2/production/done-lots?table=lotprioritydone&search=L&size=5')
                
                if response3.status_code == 200:
                    data3 = response3.get_json()
                    if data3.get('success'):
                        logger.info(f"✅ 搜索查询成功: {len(data3['data'])}条记录，筛选条件数: {data3['filters'].get('filters_applied', 0)}")
                        if data3['data']:
                            sample = data3['data'][0]
                            logger.info(f"📊 搜索结果示例: LOT_ID={sample.get('LOT_ID')}, DEVICE={sample.get('DEVICE')}")
                    else:
                        logger.error(f"❌ 搜索查询失败: {data3.get('message')}")
                else:
                    logger.error(f"❌ 搜索查询HTTP错误: {response3.status_code}")
                
                # 测试4: 排序查询
                logger.info("🧪 测试4: 排序查询（按DEVICE降序）")
                response4 = client.get('/api/v2/production/done-lots?table=lotprioritydone&sort_by=DEVICE&sort_order=DESC&size=5')
                
                if response4.status_code == 200:
                    data4 = response4.get_json()
                    if data4.get('success'):
                        logger.info(f"✅ 排序查询成功: {len(data4['data'])}条记录")
                        logger.info(f"📊 排序信息: 字段={data4['filters']['sort_by']}, 顺序={data4['filters']['sort_order']}")
                        if data4['data']:
                            devices = [item.get('DEVICE') for item in data4['data']]
                            logger.info(f"📊 排序结果: {devices}")
                    else:
                        logger.error(f"❌ 排序查询失败: {data4.get('message')}")
                else:
                    logger.error(f"❌ 排序查询HTTP错误: {response4.status_code}")
                
                # 测试5: 多字段筛选
                logger.info("🧪 测试5: 多字段筛选（设备筛选）")
                response5 = client.get('/api/v2/production/done-lots?table=lotprioritydone&device=test&priority_min=1&priority_max=100&size=5')
                
                if response5.status_code == 200:
                    data5 = response5.get_json()
                    if data5.get('success'):
                        logger.info(f"✅ 多字段筛选成功: {len(data5['data'])}条记录")
                        if 'performance' in data5:
                            perf = data5['performance']
                            logger.info(f"⚡ 性能信息: 查询耗时={perf.get('query_time', 'N/A')}ms, 连接池={perf.get('connection_pool', 'N/A')}")
                    else:
                        logger.error(f"❌ 多字段筛选失败: {data5.get('message')}")
                else:
                    logger.error(f"❌ 多字段筛选HTTP错误: {response5.status_code}")
                
                # 测试6: 错误处理（不支持的表名）
                logger.info("🧪 测试6: 错误处理（不支持的表名）")
                response6 = client.get('/api/v2/production/done-lots?table=invalid_table')
                
                if response6.status_code == 400:
                    data6 = response6.get_json()
                    logger.info(f"✅ 错误处理测试成功: {data6.get('message')}")
                else:
                    logger.warning(f"⚠️ 错误处理测试异常: HTTP {response6.status_code}")
                
                # 测试7: 获取列信息
                logger.info("🧪 测试7: 获取列信息")
                response7 = client.get('/api/v2/production/done-lots/columns?table=lotprioritydone')
                
                if response7.status_code == 200:
                    data7 = response7.get_json()
                    if data7.get('success'):
                        columns = data7.get('columns', [])
                        logger.info(f"✅ 列信息查询成功: {len(columns)}个字段")
                        logger.info(f"📊 字段示例: {[col['name'] for col in columns[:5]]}")
                    else:
                        logger.error(f"❌ 列信息查询失败: {data7.get('message')}")
                else:
                    logger.error(f"❌ 列信息查询HTTP错误: {response7.status_code}")
                
                logger.info("🎉 已排产表API接口测试完成！")
                return True
                
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_done_lots_api()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")

if __name__ == "__main__":
    main()