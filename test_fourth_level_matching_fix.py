#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试第四级匹配修复
验证是否解决200+批次只排32台机器的问题
特别验证用户案例：YQ69066LGAB-SJA1_TR1 应该可以排到 HCHC-O-014-6800
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_fourth_level_matching_fix.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestFourthLevelMatching')

def test_specific_case():
    """测试用户提到的具体案例：YQ69066LGAB-SJA1_TR1 -> HCHC-O-014-6800"""
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.real_scheduling_service import RealSchedulingService
            scheduler = RealSchedulingService()
            
            print("🔍 测试用户提到的具体案例")
            print("="*60)
            print("批次: YQ69066LGAB-SJA1_TR1")
            print("设备: HCHC-O-014-6800")
            print("")
            
            # 模拟批次需求（基于LOT_ID推测）
            lot_requirements = {
                'DEVICE': 'YQ69066LGAB',  # 从LOT_ID提取产品名
                'STAGE': 'SJA1_TR1',      # 从LOT_ID提取工序名
                'KIT_PN': '',             # 通常为空，这就是匹配失败的原因
                'HB_PN': '',              # 通常为空
                'TB_PN': '',              # 通常为空
                'HANDLER_CONFIG': '',     # 通常为空
                'EQP_CLASS': '',
                'TESTER_CONFIG': ''
            }
            
            # 模拟设备信息（基于HANDLER_ID推测）
            equipment = {
                'HANDLER_ID': 'HCHC-O-014-6800',
                'EQP_CLASS': 'PnP',              # 假设类型
                'TEMPERATURE_RANGE': '25-150',    # 假设温度范围
                'DEVICE': '',                     # 当前产品（可能为空）
                'STAGE': '',                      # 当前工序（可能为空）
                'STATUS': 'IDLE',
                'KIT_PN': '',                     # 设备配置（通常为空）
                'HB_PN': '',
                'TB_PN': '',
                'HANDLER_CONFIG': ''              # 设备配置（通常为空）
            }
            
            preloaded_data = {}
            
            print("📋 批次需求:")
            for key, value in lot_requirements.items():
                print(f"  {key}: '{value}'")
            print("")
            
            print("🔧 设备信息:")  
            for key, value in equipment.items():
                print(f"  {key}: '{value}'")
            print("")
            
            # 测试修复前的匹配逻辑（三级匹配）
            print("🧪 测试三级传统匹配:")
            
            # 1. 同设置匹配
            same_setup = scheduler._check_sql_same_setup_match(
                lot_requirements.get('KIT_PN', ''),
                lot_requirements.get('HB_PN', ''),
                lot_requirements.get('TB_PN', ''),
                lot_requirements.get('DEVICE', ''),
                lot_requirements.get('STAGE', ''),
                equipment.get('KIT_PN', ''),
                equipment.get('HB_PN', ''),
                equipment.get('TB_PN', ''),
                equipment.get('DEVICE', ''),
                equipment.get('STAGE', ''),
                preloaded_data
            )
            print(f"  1️⃣ 同设置匹配: {same_setup}")
            
            # 2. 小改机匹配  
            small_change = scheduler._check_sql_small_change_match(
                lot_requirements.get('KIT_PN', ''),
                lot_requirements.get('HB_PN', ''),
                lot_requirements.get('TB_PN', ''),
                lot_requirements.get('DEVICE', ''),
                lot_requirements.get('STAGE', ''),
                equipment.get('KIT_PN', ''),
                equipment.get('HB_PN', ''),
                equipment.get('TB_PN', ''),
                equipment.get('DEVICE', ''),
                equipment.get('STAGE', ''),
                preloaded_data
            )
            print(f"  2️⃣ 小改机匹配: {small_change}")
            
            # 3. 大改机匹配
            big_change = scheduler._check_sql_big_change_match(
                lot_requirements.get('HANDLER_CONFIG', ''),
                lot_requirements.get('DEVICE', ''),
                lot_requirements.get('STAGE', ''),
                equipment.get('HANDLER_CONFIG', ''),
                equipment.get('DEVICE', ''),
                equipment.get('STAGE', ''),
                preloaded_data,
                equipment
            )
            print(f"  3️⃣ 大改机匹配: {big_change}")
            
            # 4. 测试新增的第四级匹配
            print("")
            print("🌡️ 测试新增的第四级温度感知匹配:")
            fourth_level = scheduler._check_temperature_aware_device_stage_match(
                lot_requirements.get('DEVICE', ''),
                lot_requirements.get('STAGE', ''),
                equipment.get('DEVICE', ''),
                equipment.get('STAGE', ''),
                equipment,
                preloaded_data
            )
            print(f"  4️⃣ 温度感知匹配: {fourth_level}")
            
            # 5. 测试完整的设备匹配评分
            print("")
            print("⚖️ 测试完整设备匹配评分:")
            score, match_type, changeover_time = scheduler.calculate_equipment_match_score_optimized(
                lot_requirements, equipment, preloaded_data
            )
            print(f"  最终评分: {score}分")
            print(f"  匹配类型: {match_type}")
            print(f"  改机时间: {changeover_time}分钟")
            
            print("")
            print("="*60)
            
            # 判断修复效果
            traditional_match = same_setup or small_change or big_change
            any_match = traditional_match or fourth_level
            
            if score > 0:
                print("🎉 修复成功！批次现在可以匹配到该设备了")
                print(f"   - 评分: {score}分 (>0 表示可以排产)")
                print(f"   - 匹配方式: {match_type}")
                if not traditional_match and fourth_level:
                    print("   - 🌡️ 通过新增的第四级温度感知匹配成功")
                return True
            else:
                print("❌ 仍然无法匹配")
                print(f"   - 传统三级匹配: {traditional_match}")
                print(f"   - 第四级匹配: {fourth_level}")
                print("   - 需要进一步分析批次和设备的具体信息")
                return False
                
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_overall_improvement():
    """测试整体改进效果：模拟更多批次和设备的匹配"""
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.real_scheduling_service import RealSchedulingService
            scheduler = RealSchedulingService()
            
            print("\n📊 测试整体改进效果")
            print("="*60)
            
            # 模拟多种批次类型
            test_batches = [
                {'DEVICE': 'YQ69066LGAB', 'STAGE': 'SJA1_TR1', 'description': '用户案例批次'},
                {'DEVICE': 'JWH7030QFNAZ', 'STAGE': 'Cold', 'description': '低温测试批次'},
                {'DEVICE': 'ABC123DEF', 'STAGE': 'Hot', 'description': '高温测试批次'},
                {'DEVICE': 'XYZ789GHI', 'STAGE': 'ROOM-TTR', 'description': '常温TTR批次'},
                {'DEVICE': 'TEST001', 'STAGE': 'BAKING', 'description': '烘烤测试批次'},
            ]
            
            # 模拟多种设备类型
            test_equipment = [
                {'HANDLER_ID': 'HCHC-O-014-6800', 'EQP_CLASS': 'PnP', 'TEMPERATURE_RANGE': '25-150'},
                {'HANDLER_ID': 'HCHC-C-033-6800', 'EQP_CLASS': 'PnP', 'TEMPERATURE_RANGE': '-50-150'},
                {'HANDLER_ID': 'TE-TUR-001', 'EQP_CLASS': 'Turret', 'TEMPERATURE_RANGE': '25'},
                {'HANDLER_ID': 'TE-TUR-002', 'EQP_CLASS': 'Turret', 'TEMPERATURE_RANGE': '-50-150'},
                {'HANDLER_ID': 'TE-PNP-003', 'EQP_CLASS': 'PnP', 'TEMPERATURE_RANGE': '25'},
            ]
            
            total_combinations = len(test_batches) * len(test_equipment)
            successful_matches = 0
            fourth_level_matches = 0
            
            print(f"🔍 测试 {len(test_batches)} 个批次 × {len(test_equipment)} 台设备 = {total_combinations} 个组合")
            print("")
            
            for batch in test_batches:
                print(f"📦 批次: {batch['DEVICE']}-{batch['STAGE']} ({batch['description']})")
                
                batch_requirements = {
                    'DEVICE': batch['DEVICE'],
                    'STAGE': batch['STAGE'],
                    'KIT_PN': '', 'HB_PN': '', 'TB_PN': '', 'HANDLER_CONFIG': '', 'EQP_CLASS': '', 'TESTER_CONFIG': ''
                }
                
                batch_matches = 0
                batch_fourth_level = 0
                
                for equipment in test_equipment:
                    # 完整设备信息
                    full_equipment = {
                        **equipment,
                        'DEVICE': '', 'STAGE': '', 'STATUS': 'IDLE',
                        'KIT_PN': '', 'HB_PN': '', 'TB_PN': '', 'HANDLER_CONFIG': ''
                    }
                    
                    # 测试匹配
                    score, match_type, _ = scheduler.calculate_equipment_match_score_optimized(
                        batch_requirements, full_equipment, {}
                    )
                    
                    if score > 0:
                        successful_matches += 1
                        batch_matches += 1
                        
                        if match_type == "温度感知匹配":
                            fourth_level_matches += 1
                            batch_fourth_level += 1
                
                print(f"  ✅ 可匹配设备: {batch_matches}/{len(test_equipment)} (其中{batch_fourth_level}个通过第四级匹配)")
            
            print("")
            print("="*60)
            print("📈 整体改进效果:")
            print(f"  总组合数: {total_combinations}")
            print(f"  成功匹配: {successful_matches} ({100*successful_matches/total_combinations:.1f}%)")
            print(f"  第四级匹配贡献: {fourth_level_matches} ({100*fourth_level_matches/total_combinations:.1f}%)")
            print(f"  改进效果: 第四级匹配使 {fourth_level_matches} 个原本无法匹配的组合成功匹配")
            
            return successful_matches > 0 and fourth_level_matches > 0
            
    except Exception as e:
        logger.error(f"❌ 整体测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主测试函数"""
    print("🚀 第四级匹配修复验证测试")
    print("目标：解决200+批次只排32台机器的问题")
    print("")
    
    # 测试1：具体案例
    test1_success = test_specific_case()
    
    # 测试2：整体改进
    test2_success = test_overall_improvement()
    
    print("\n" + "="*60)
    print("🎯 测试总结:")
    
    if test1_success and test2_success:
        print("🎉 修复验证成功！")
        print("  ✅ 用户提到的具体案例现在可以匹配")
        print("  ✅ 第四级温度感知匹配有效扩大了设备利用率")
        print("  ✅ 解决了200+批次只排32台机器的根本问题")
        print("")
        print("💡 修复原理:")
        print("  1. 发现问题：三级严格匹配（KIT_PN/HB_PN/TB_PN/HANDLER_CONFIG）过于严格")
        print("  2. 添加第四级：基于温度能力的DEVICE+STAGE智能匹配")  
        print("  3. 集成约束：保留EQP_CLASS等业务规则约束")
        print("  4. 提升利用率：更多设备可以参与排产，解决设备利用率低的问题")
        
        return True
    else:
        print("❌ 修复验证失败")
        print("  需要进一步调试和优化")
        return False

if __name__ == "__main__":
    main()