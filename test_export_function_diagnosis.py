#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
triggerUnifiedExport 导出功能诊断脚本
检查前端导出功能失效的具体原因
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
from datetime import datetime
import requests

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('export_diagnosis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('ExportDiagnosis')

def check_static_files():
    """检查前端静态文件是否存在"""
    logger.info("🔍 检查前端静态文件...")
    
    required_files = [
        'app/static/js/done_lots_data_manager.js',
        'app/static/js/done_lots_export_manager.js', 
        'app/static/js/done_lots_filter_ui.js',
        'app/static/js/done_lots_pagination.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            logger.error(f"❌ 缺失文件: {file_path}")
        else:
            logger.info(f"✅ 存在文件: {file_path}")
    
    return len(missing_files) == 0

def check_template_integration():
    """检查模板文件中的集成"""
    logger.info("🔍 检查模板文件集成...")
    
    template_path = 'app/templates/production/done_lots.html'
    if not os.path.exists(template_path):
        logger.error(f"❌ 模板文件不存在: {template_path}")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键组件
    checks = {
        'triggerUnifiedExport': 'triggerUnifiedExport函数',
        'loadUnifiedComponents': '组件加载函数',
        'done_lots_data_manager.js': '数据管理器引用',
        'done_lots_export_manager.js': '导出管理器引用',
        'done_lots_filter_ui.js': '筛选UI引用',
        'done_lots_pagination.js': '分页组件引用',
        'window.doneLotsExportManager': '全局导出管理器变量'
    }
    
    all_present = True
    for check, description in checks.items():
        if check in content:
            logger.info(f"✅ 模板包含: {description}")
        else:
            logger.error(f"❌ 模板缺失: {description}")
            all_present = False
    
    return all_present

def test_backend_apis():
    """测试后端API接口"""
    logger.info("🔍 测试后端API接口...")
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            logger.info("✅ Flask应用创建成功")
            
            # 检查API路由是否注册
            api_routes = []
            for rule in app.url_map.iter_rules():
                if 'done-lots' in rule.rule and 'export' in rule.rule:
                    api_routes.append((rule.rule, rule.methods))
                    logger.info(f"✅ 发现导出API: {rule.rule} {rule.methods}")
            
            if not api_routes:
                logger.error("❌ 未找到导出相关的API路由")
                return False
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Flask应用创建失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_export_task_manager():
    """检查导出任务管理器"""
    logger.info("🔍 检查导出任务管理器...")
    
    try:
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            from app.services.export_task_manager import export_task_manager
            logger.info("✅ 导出任务管理器导入成功")
            
            # 检查任务管理器的基本功能
            if hasattr(export_task_manager, 'create_task'):
                logger.info("✅ 任务管理器具有create_task方法")
            else:
                logger.error("❌ 任务管理器缺少create_task方法")
                
            if hasattr(export_task_manager, 'get_task_status'):
                logger.info("✅ 任务管理器具有get_task_status方法")
            else:
                logger.error("❌ 任务管理器缺少get_task_status方法")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 导出任务管理器检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def check_database_data():
    """检查数据库中是否有已调度的数据"""
    logger.info("🔍 检查数据库中的已调度数据...")
    
    try:
        from app import create_app
        from app.models import db
        
        app, socketio = create_app()
        
        with app.app_context():
            # 检查主要数据表
            tables_to_check = [
                ('lotprioritydone', '已排产批次表'),
                ('final_scheduling_result', '最终排产结果表'),
                ('et_wait_lot', '待排产批次表')
            ]
            
            for table_name, description in tables_to_check:
                try:
                    result = db.engine.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = result.fetchone()[0]
                    logger.info(f"✅ {description}: {count} 条记录")
                    
                    if count == 0:
                        logger.warning(f"⚠️ {description} 为空，可能没有数据可导出")
                        
                except Exception as e:
                    logger.error(f"❌ 检查{description}失败: {e}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 数据库检查失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def generate_troubleshooting_report():
    """生成故障排除报告"""
    logger.info("📊 生成故障排除报告...")
    
    report = []
    report.append("=" * 60)
    report.append("triggerUnifiedExport 导出功能故障诊断报告")
    report.append("=" * 60)
    report.append(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # 收集诊断结果
    checks = [
        ("静态文件检查", check_static_files),
        ("模板集成检查", check_template_integration),
        ("后端API检查", test_backend_apis),
        ("导出任务管理器检查", check_export_task_manager),
        ("数据库数据检查", check_database_data)
    ]
    
    failed_checks = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            status = "✅ 通过" if result else "❌ 失败"
            report.append(f"{check_name}: {status}")
            if not result:
                failed_checks.append(check_name)
        except Exception as e:
            report.append(f"{check_name}: ❌ 异常 - {str(e)}")
            failed_checks.append(check_name)
    
    report.append("")
    report.append("可能的问题原因和解决方案:")
    report.append("-" * 40)
    
    if "静态文件检查" in failed_checks:
        report.append("❌ 前端JavaScript文件缺失")
        report.append("   解决方案: 确保所有必需的JS文件存在于app/static/js/目录下")
        report.append("")
    
    if "模板集成检查" in failed_checks:
        report.append("❌ 模板文件集成问题")
        report.append("   解决方案: 检查done_lots.html模板中的JavaScript引用和函数定义")
        report.append("")
    
    if "后端API检查" in failed_checks:
        report.append("❌ 后端API接口问题")
        report.append("   解决方案: 检查API路由注册和服务启动状态")
        report.append("")
    
    if "导出任务管理器检查" in failed_checks:
        report.append("❌ 导出任务管理器问题")
        report.append("   解决方案: 检查export_task_manager.py文件和相关依赖")
        report.append("")
    
    if "数据库数据检查" in failed_checks:
        report.append("❌ 数据库数据问题")
        report.append("   解决方案: 确保数据库中有已调度的批次数据")
        report.append("")
    
    if not failed_checks:
        report.append("✅ 所有检查都通过了！")
        report.append("   如果导出功能仍然失效，可能是浏览器缓存问题")
        report.append("   建议: 清除浏览器缓存并刷新页面")
        report.append("   或者在浏览器控制台检查JavaScript错误")
    
    report.append("")
    report.append("调试建议:")
    report.append("-" * 20)
    report.append("1. 打开浏览器开发者工具(F12)")
    report.append("2. 查看Console选项卡中的JavaScript错误")
    report.append("3. 检查Network选项卡中API调用是否成功")
    report.append("4. 在Console中输入: window.doneLotsExportManager")
    report.append("   应该看到导出管理器对象，如果是undefined说明初始化失败")
    report.append("5. 在Console中输入: typeof DoneLotsExportManager")
    report.append("   应该显示'function'，如果是'undefined'说明类文件未加载")
    
    # 保存报告到文件
    report_text = "\n".join(report)
    with open('export_diagnosis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_text)
    
    logger.info("📊 故障诊断报告已生成: export_diagnosis_report.txt")
    print("\n" + report_text)

def main():
    """主函数"""
    logger.info("🚀 开始triggerUnifiedExport导出功能诊断...")
    
    try:
        generate_troubleshooting_report()
        logger.info("🎉 诊断完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 诊断过程失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = main()
    print("🎉 诊断完成，请查看 export_diagnosis_report.txt 文件" if success else "❌ 诊断失败")