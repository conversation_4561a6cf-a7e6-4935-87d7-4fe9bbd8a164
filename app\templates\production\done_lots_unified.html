{% extends "base.html" %}

{% set page_title = "已排产批次" %}
{% set table_title = "已排产批次" %}
{% set page_description = "管理已排产批次信息，包含内部工单号、产品名称、工序、数量、排产时间等。支持筛选、编辑、删除等操作。" %}

{% block title %}{{ page_title }} - AEC-FT ICP{% endblock %}

{% block extra_css %}
<!-- 引入Bootstrap和FontAwesome -->
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/bootstrap.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/all.min.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/theme.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom/production-tables.css') }}">

<style>
/* 统一页面样式 */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #b72424 0%, #b72424 100%);
    color: rgb(255, 255, 255);
    font-weight: bold;
}

/* 按钮样式 */
.btn-console {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    line-height: 1.5;
    border-radius: 0.375rem;
    white-space: nowrap;
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 6px;
    padding: 0.6rem;
    text-align: center;
    border: 1px solid #dee2e6;
}

.stats-number {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.15rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.8rem;
    margin: 0;
}

/* 模式切换样式 */
.mode-toggle-section {
    border-bottom: 2px solid #f8f9fa;
    padding-bottom: 1rem;
}

.btn-check:checked + .btn-outline-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-check:checked + .btn-outline-danger {
    background-color: #b72424;
    border-color: #b72424;
    color: white;
}

.btn-check:checked + .btn-outline-success {
    background-color: #198754;
    border-color: #198754;
    color: white;
}

/* 表格容器样式 */
.table-container {
    min-height: 400px;
    position: relative;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.table-responsive-custom {
    max-height: 600px;
    overflow-y: auto;
}

.table th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 5;
}

/* 状态样式 */
.status-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 500;
    color: white;
}

.status-success { background-color: #28a745; }
.status-warning { background-color: #ffc107; color: #212529; }
.status-danger { background-color: #dc3545; }
.status-info { background-color: #17a2b8; }

/* 优先级样式 */
.priority-high { color: #dc3545; font-weight: bold; }
.priority-medium { color: #ffc107; font-weight: bold; }
.priority-low { color: #28a745; font-weight: normal; }

/* 调整模式特殊样式 */
.adjustment-workspace {
    min-height: 500px;
}

.handler-groups {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    max-height: 600px;
    overflow-y: auto;
}

.handler-group {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    background: #f8f9fa;
}

.handler-group-header {
    background: #e9ecef;
    padding: 0.75rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: bold;
}

.lot-item {
    padding: 0.5rem;
    margin: 0.25rem;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 0.25rem;
    cursor: move;
}

.lot-item:hover {
    background: #f0f8ff;
    border-color: #007bff;
}

.lot-item.dragging {
    opacity: 0.5;
}

.drop-zone {
    border: 2px dashed #007bff;
    background: rgba(0, 123, 255, 0.1);
    min-height: 60px;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #007bff;
    margin: 0.25rem;
}

.drop-zone.dragover {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <!-- 页面头部 -->
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-tasks me-2"></i>{{ page_title }}
                        </h5>
                        <div>
                            <button type="button" class="btn btn-outline-light btn-console me-2" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新
                            </button>
                            <button type="button" class="btn btn-outline-light btn-console" onclick="exportData()">
                                <i class="fas fa-file-excel me-1"></i>导出数据
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- 模式切换区域 -->
                    <div class="mode-toggle-section mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="btn-group" role="group">
                                <input type="radio" class="btn-check" name="viewMode" id="viewModeBtn" checked>
                                <label class="btn btn-outline-primary" for="viewModeBtn">
                                    <i class="fas fa-table me-1"></i>查看模式
                                </label>
                                
                                <input type="radio" class="btn-check" name="viewMode" id="adjustModeBtn">
                                <label class="btn btn-outline-danger" for="adjustModeBtn">
                                    <i class="fas fa-tools me-1"></i>调整模式
                                </label>
                                
                                <input type="radio" class="btn-check" name="viewMode" id="finalResultModeBtn">
                                <label class="btn btn-outline-success" for="finalResultModeBtn">
                                    <i class="fas fa-check-circle me-1"></i>最终结果
                                </label>
                            </div>
                            
                            <div class="mode-actions">
                                <button type="button" class="btn btn-success btn-console me-2" id="saveAdjustmentsBtn" style="display: none;">
                                    <i class="fas fa-save me-1"></i>保存调整
                                </button>
                                <button type="button" class="btn btn-secondary btn-console" id="cancelAdjustmentsBtn" style="display: none;">
                                    <i class="fas fa-times me-1"></i>取消调整
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="row mb-4" id="statisticsSection">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-primary" id="totalRecords">0</div>
                                <p class="stats-label">总记录数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-success" id="totalQuantity">0</div>
                                <p class="stats-label">总数量</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-info" id="handlerCount">0</div>
                                <p class="stats-label">分选机数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <div class="stats-number text-warning" id="avgScore">0</div>
                                <p class="stats-label">平均评分</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 筛选区域 -->
                    <div id="filterContainer" class="mb-3"></div>
                    
                    <!-- 数据展示区域 -->
                    <div class="table-container" id="dataContainer">
                        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                        
                        <!-- 查看模式和最终结果模式的表格 -->
                        <div id="tableContainer" style="display: block;">
                            <div class="table-responsive-custom">
                                <table class="table table-hover table-sm" id="dataTable">
                                    <thead>
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" id="selectAll">
                                            </th>
                                            <th>优先级</th>
                                            <th>内部工单号</th>
                                            <th>产品名称</th>
                                            <th>分选机</th>
                                            <th>数量</th>
                                            <th>评分</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th width="120">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tableBody">
                                        <!-- 数据将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 调整模式的分组显示 -->
                        <div id="adjustmentContainer" style="display: none;">
                            <div class="adjustment-workspace">
                                <div class="handler-groups" id="handlerGroups">
                                    <!-- 分选机分组将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 分页区域 -->
                    <div id="paginationContainer" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 引入新开发的组件 -->
<script src="{{ url_for('static', filename='js/done_lots_data_manager.js') }}"></script>
<script src="{{ url_for('static', filename='js/done_lots_filter_ui.js') }}"></script>
<script src="{{ url_for('static', filename='js/done_lots_pagination.js') }}"></script>

<script>
// 全局变量
let dataManager;
let filterUI;
let pagination;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 初始化已排产批次页面...');
    
    try {
        // 初始化数据管理器
        dataManager = new DoneLotsDataManager();
        
        // 初始化筛选UI
        filterUI = new DoneLotsFilterUI('filterContainer', dataManager);
        
        // 初始化分页组件
        pagination = new DoneLotsPagination('paginationContainer', dataManager);
        
        // 设置数据管理器的渲染回调
        dataManager.setRenderCallback(renderData);
        dataManager.setStatisticsCallback(updateStatistics);
        
        // 绑定模式切换事件
        bindModeToggleEvents();
        
        // 绑定其他事件
        bindUIEvents();
        
        // 初始加载数据
        dataManager.setMode('view');
        dataManager.loadData();
        
        console.log('✅ 页面初始化完成');
        
    } catch (error) {
        console.error('❌ 页面初始化失败:', error);
        showNotification('页面初始化失败: ' + error.message, 'danger');
    }
});

// 绑定模式切换事件
function bindModeToggleEvents() {
    const viewModeBtn = document.getElementById('viewModeBtn');
    const adjustModeBtn = document.getElementById('adjustModeBtn');
    const finalResultModeBtn = document.getElementById('finalResultModeBtn');
    
    viewModeBtn.addEventListener('change', function() {
        if (this.checked) {
            switchMode('view');
        }
    });
    
    adjustModeBtn.addEventListener('change', function() {
        if (this.checked) {
            switchMode('adjust');
        }
    });
    
    finalResultModeBtn.addEventListener('change', function() {
        if (this.checked) {
            switchMode('final_result');
        }
    });
}

// 切换模式
function switchMode(mode) {
    console.log(`🔄 切换到${mode}模式`);
    
    const tableContainer = document.getElementById('tableContainer');
    const adjustmentContainer = document.getElementById('adjustmentContainer');
    const saveBtn = document.getElementById('saveAdjustmentsBtn');
    const cancelBtn = document.getElementById('cancelAdjustmentsBtn');
    
    // 显示/隐藏相应容器
    if (mode === 'adjust') {
        tableContainer.style.display = 'none';
        adjustmentContainer.style.display = 'block';
        saveBtn.style.display = 'inline-block';
        cancelBtn.style.display = 'inline-block';
    } else {
        tableContainer.style.display = 'block';
        adjustmentContainer.style.display = 'none';
        saveBtn.style.display = 'none';
        cancelBtn.style.display = 'none';
    }
    
    // 设置数据管理器模式并加载数据
    dataManager.setMode(mode);
    dataManager.loadData();
}

// 绑定UI事件
function bindUIEvents() {
    // 全选复选框
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('#tableBody input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = this.checked);
        updateBatchOperations();
    });
    
    // 保存调整按钮
    document.getElementById('saveAdjustmentsBtn').addEventListener('click', saveAdjustments);
    
    // 取消调整按钮
    document.getElementById('cancelAdjustmentsBtn').addEventListener('click', cancelAdjustments);
}

// 渲染数据
function renderData(data, mode) {
    console.log(`📊 渲染${mode}模式数据:`, data.data?.length || 0, '条记录');
    
    showLoading(false);
    
    if (mode === 'adjust') {
        renderAdjustmentMode(data);
    } else {
        renderTableMode(data, mode);
    }
}

// 渲染表格模式
function renderTableMode(data, mode) {
    const tbody = document.getElementById('tableBody');
    tbody.innerHTML = '';
    
    if (!data.data || data.data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="10" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    data.data.forEach(item => {
        const row = createTableRow(item, mode);
        tbody.appendChild(row);
    });
    
    // 绑定行点击事件
    bindRowEvents();
}

// 创建表格行
function createTableRow(item, mode) {
    const row = document.createElement('tr');
    row.dataset.id = item.id || item.lot_id;
    
    const priorityClass = item.priority > 80 ? 'priority-high' : 
                         item.priority > 50 ? 'priority-medium' : 'priority-low';
    
    const statusClass = item.status === 'completed' ? 'status-success' :
                       item.status === 'running' ? 'status-warning' :
                       item.status === 'failed' ? 'status-danger' : 'status-info';
    
    row.innerHTML = `
        <td>
            <input type="checkbox" class="row-checkbox" value="${item.id || item.lot_id}">
        </td>
        <td class="${priorityClass}">${item.priority || '-'}</td>
        <td><strong>${item.lot_id || '-'}</strong></td>
        <td>${item.device || item.product_name || '-'}</td>
        <td>${item.handler_id || '-'}</td>
        <td class="text-end">${formatNumber(item.quantity || 0)}</td>
        <td class="text-center">${(item.score || 0).toFixed(1)}</td>
        <td>
            <span class="status-badge ${statusClass}">
                ${getStatusText(item.status)}
            </span>
        </td>
        <td>${formatDateTime(item.create_time || item.CREATE_TIME)}</td>
        <td>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="viewDetail(${item.id || item.lot_id})">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="editItem(${item.id || item.lot_id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="deleteItem(${item.id || item.lot_id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// 渲染调整模式
function renderAdjustmentMode(data) {
    const container = document.getElementById('handlerGroups');
    container.innerHTML = '';
    
    if (!data.data || data.data.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-4">暂无数据</div>';
        return;
    }
    
    // 按分选机分组
    const handlerGroups = {};
    data.data.forEach(item => {
        const handlerId = item.handler_id || '未分配';
        if (!handlerGroups[handlerId]) {
            handlerGroups[handlerId] = [];
        }
        handlerGroups[handlerId].push(item);
    });
    
    // 渲染每个分选机组
    Object.entries(handlerGroups).forEach(([handlerId, items]) => {
        const groupElement = createHandlerGroup(handlerId, items);
        container.appendChild(groupElement);
    });
    
    // 初始化拖拽功能
    initializeDragAndDrop();
}

// 创建分选机分组
function createHandlerGroup(handlerId, items) {
    const groupDiv = document.createElement('div');
    groupDiv.className = 'handler-group';
    groupDiv.dataset.handlerId = handlerId;
    
    const totalQuantity = items.reduce((sum, item) => sum + (item.quantity || 0), 0);
    
    groupDiv.innerHTML = `
        <div class="handler-group-header">
            <div class="d-flex justify-content-between align-items-center">
                <strong>${handlerId}</strong>
                <span class="badge bg-info">${items.length}批次 / ${formatNumber(totalQuantity)}件</span>
            </div>
        </div>
        <div class="lots-container" data-handler="${handlerId}">
            ${items.map(item => createLotItem(item)).join('')}
            <div class="drop-zone" data-handler="${handlerId}">
                <i class="fas fa-plus me-2"></i>拖放批次到这里
            </div>
        </div>
    `;
    
    return groupDiv;
}

// 创建批次项
function createLotItem(item) {
    const priorityClass = item.priority > 80 ? 'priority-high' : 
                         item.priority > 50 ? 'priority-medium' : 'priority-low';
    
    return `
        <div class="lot-item" draggable="true" data-lot-id="${item.lot_id}" data-handler="${item.handler_id}">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${item.lot_id}</strong>
                    <br>
                    <small class="text-muted">${item.device || item.product_name || '-'}</small>
                </div>
                <div class="text-end">
                    <div class="${priorityClass}">${item.priority || '-'}</div>
                    <small>${formatNumber(item.quantity || 0)}件</small>
                </div>
            </div>
        </div>
    `;
}

// 初始化拖拽功能
function initializeDragAndDrop() {
    const lotItems = document.querySelectorAll('.lot-item');
    const dropZones = document.querySelectorAll('.drop-zone');
    
    // 拖拽开始
    lotItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            this.classList.add('dragging');
            e.dataTransfer.setData('text/plain', JSON.stringify({
                lotId: this.dataset.lotId,
                fromHandler: this.dataset.handler
            }));
        });
        
        item.addEventListener('dragend', function() {
            this.classList.remove('dragging');
        });
    });
    
    // 拖拽目标
    dropZones.forEach(zone => {
        zone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        zone.addEventListener('dragleave', function() {
            this.classList.remove('dragover');
        });
        
        zone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const data = JSON.parse(e.dataTransfer.getData('text/plain'));
            const toHandler = this.dataset.handler;
            
            if (data.fromHandler !== toHandler) {
                moveLotToHandler(data.lotId, data.fromHandler, toHandler);
            }
        });
    });
}

// 移动批次到分选机
function moveLotToHandler(lotId, fromHandler, toHandler) {
    console.log(`🔄 移动批次 ${lotId} 从 ${fromHandler} 到 ${toHandler}`);
    // 这里可以添加实际的移动逻辑
    showNotification(`批次 ${lotId} 已移动到 ${toHandler}`, 'success');
    
    // 重新加载数据以反映更改
    dataManager.loadData();
}

// 更新统计信息
function updateStatistics(stats) {
    document.getElementById('totalRecords').textContent = stats.totalRecords || 0;
    document.getElementById('totalQuantity').textContent = formatNumber(stats.totalQuantity || 0);
    document.getElementById('handlerCount').textContent = stats.handlerCount || 0;
    document.getElementById('avgScore').textContent = (stats.avgScore || 0).toFixed(1);
}

// 绑定行事件
function bindRowEvents() {
    const checkboxes = document.querySelectorAll('.row-checkbox');
    checkboxes.forEach(cb => {
        cb.addEventListener('change', updateBatchOperations);
    });
}

// 更新批量操作
function updateBatchOperations() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    const batchOps = document.getElementById('batchOperations');
    const selectedCount = document.getElementById('selectedCount');
    
    if (checkedBoxes.length > 0) {
        batchOps.style.display = 'block';
        selectedCount.textContent = checkedBoxes.length;
    } else {
        batchOps.style.display = 'none';
    }
}

// 显示加载状态
function showLoading(show = true) {
    const overlay = document.getElementById('loadingOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// 刷新数据
function refreshData() {
    console.log('🔄 刷新数据...');
    showLoading(true);
    dataManager.loadData();
}

// 导出数据
function exportData() {
    console.log('📥 导出数据...');
    const currentMode = dataManager.getCurrentMode();
    const filters = filterUI.getFilters();
    
    // 构建导出URL
    let exportUrl = '/api/v2/production/done-lots/export?mode=' + currentMode;
    
    // 添加筛选条件
    if (Object.keys(filters).length > 0) {
        exportUrl += '&' + new URLSearchParams(filters).toString();
    }
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `已排产批次_${currentMode}_${formatDate(new Date())}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('导出请求已提交，请稍候...', 'info');
}

// 保存调整
function saveAdjustments() {
    console.log('💾 保存调整...');
    // 这里添加保存调整的逻辑
    showNotification('调整已保存', 'success');
}

// 取消调整
function cancelAdjustments() {
    console.log('❌ 取消调整...');
    dataManager.loadData(); // 重新加载原始数据
    showNotification('调整已取消', 'info');
}

// 查看详情
function viewDetail(id) {
    console.log('👁️ 查看详情:', id);
    // 实现查看详情逻辑
}

// 编辑项目
function editItem(id) {
    console.log('✏️ 编辑项目:', id);
    // 实现编辑逻辑
}

// 删除项目
function deleteItem(id) {
    if (confirm('确定要删除这个项目吗？')) {
        console.log('🗑️ 删除项目:', id);
        // 实现删除逻辑
        showNotification('项目已删除', 'success');
        dataManager.loadData();
    }
}

// 工具函数
function formatNumber(num) {
    return new Intl.NumberFormat('zh-CN').format(num);
}

function formatDateTime(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function getStatusText(status) {
    const statusMap = {
        'completed': '已完成',
        'running': '进行中',
        'failed': '失败',
        'scheduled': '已排产'
    };
    return statusMap[status] || status || '未知';
}

function showNotification(message, type = 'info') {
    // 这里可以集成现有的通知系统
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 简单的临时通知实现
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}