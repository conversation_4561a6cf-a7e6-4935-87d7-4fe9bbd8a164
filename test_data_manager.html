<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DoneLotsDataManager 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .controls {
            margin: 15px 0;
        }
        
        .controls label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        
        .controls input, .controls select {
            padding: 5px 10px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .button-group {
            margin: 15px 0;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        
        .status.loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        
        .status.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        
        .data-display {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }
        
        .data-table th,
        .data-table td {
            padding: 5px 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .pagination-info {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .log-output {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 DoneLotsDataManager 测试页面</h1>
        
        <!-- 模式切换 -->
        <div class="test-section">
            <h3>📋 模式切换测试</h3>
            <div class="controls">
                <label>当前模式:</label>
                <select id="modeSelect">
                    <option value="view">查看模式</option>
                    <option value="adjust">调整模式</option>
                    <option value="final_result">最终结果模式</option>
                </select>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="changeMode()">切换模式</button>
            </div>
            <div id="modeStatus" class="status">当前模式: view</div>
        </div>
        
        <!-- 筛选条件测试 -->
        <div class="test-section">
            <h3>🔍 筛选条件测试</h3>
            <div class="controls">
                <div>
                    <label>全局搜索:</label>
                    <input type="text" id="searchInput" placeholder="输入搜索关键字">
                </div>
                <div>
                    <label>批次号:</label>
                    <input type="text" id="lotIdInput" placeholder="LOT_ID">
                </div>
                <div>
                    <label>产品名称:</label>
                    <input type="text" id="deviceInput" placeholder="DEVICE">
                </div>
                <div>
                    <label>分选机ID:</label>
                    <input type="text" id="handlerIdInput" placeholder="HANDLER_ID">
                </div>
                <div>
                    <label>优先级范围:</label>
                    <input type="number" id="priorityMin" placeholder="最小值" style="width: 80px;">
                    <span> - </span>
                    <input type="number" id="priorityMax" placeholder="最大值" style="width: 80px;">
                </div>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="applyFilters()">应用筛选</button>
                <button class="btn btn-warning" onclick="clearFilters()">清空筛选</button>
            </div>
            <div id="filtersStatus" class="status">筛选条件: 无</div>
        </div>
        
        <!-- 分页测试 -->
        <div class="test-section">
            <h3>📄 分页测试</h3>
            <div class="controls">
                <div>
                    <label>页码:</label>
                    <input type="number" id="pageInput" min="1" value="1" style="width: 80px;">
                </div>
                <div>
                    <label>每页条数:</label>
                    <select id="pageSizeSelect">
                        <option value="10">10条/页</option>
                        <option value="25">25条/页</option>
                        <option value="50" selected>50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="goToPage()">跳转页面</button>
                <button class="btn btn-primary" onclick="changePageSize()">改变页面大小</button>
                <button class="btn btn-success" onclick="loadData()">加载数据</button>
                <button class="btn btn-success" onclick="refreshData()">刷新数据</button>
            </div>
            <div id="paginationInfo" class="pagination-info">分页信息: 未加载</div>
        </div>
        
        <!-- 排序测试 -->
        <div class="test-section">
            <h3>📊 排序测试</h3>
            <div class="controls">
                <div>
                    <label>排序字段:</label>
                    <select id="sortField">
                        <option value="PRIORITY" selected>优先级</option>
                        <option value="CREATE_TIME">创建时间</option>
                        <option value="LOT_ID">批次号</option>
                        <option value="DEVICE">产品名称</option>
                        <option value="GOOD_QTY">良品数量</option>
                    </select>
                </div>
                <div>
                    <label>排序顺序:</label>
                    <select id="sortOrder">
                        <option value="ASC" selected>升序</option>
                        <option value="DESC">降序</option>
                    </select>
                </div>
            </div>
            <div class="button-group">
                <button class="btn btn-primary" onclick="applySort()">应用排序</button>
            </div>
            <div id="sortStatus" class="status">排序: PRIORITY ASC</div>
        </div>
        
        <!-- 失败批次测试 -->
        <div class="test-section">
            <h3>❌ 失败批次测试</h3>
            <div class="controls">
                <div>
                    <label>时间范围:</label>
                    <select id="failedHours">
                        <option value="24" selected>最近24小时</option>
                        <option value="72">最近3天</option>
                        <option value="168">最近7天</option>
                    </select>
                </div>
                <div>
                    <label>数据范围:</label>
                    <select id="failedCurrentOnly">
                        <option value="true" selected>仅当前数据</option>
                        <option value="false">全部历史数据</option>
                    </select>
                </div>
            </div>
            <div class="button-group">
                <button class="btn btn-danger" onclick="loadFailedLots()">加载失败批次</button>
            </div>
            <div id="failedLotsStatus" class="status">失败批次: 未加载</div>
        </div>
        
        <!-- 数据显示 -->
        <div class="test-section">
            <h3>📊 数据显示</h3>
            <div id="dataDisplay" class="data-display">
                <p>点击"加载数据"按钮开始测试...</p>
            </div>
        </div>
        
        <!-- 操作日志 -->
        <div class="test-section">
            <h3>📝 操作日志</h3>
            <div class="button-group">
                <button class="btn btn-warning" onclick="clearLog()">清空日志</button>
            </div>
            <div id="logOutput" class="log-output">
                等待操作...
            </div>
        </div>
    </div>
    
    <!-- 加载数据管理器 -->
    <script src="/static/js/done_lots_data_manager.js"></script>
    
    <script>
        // 全局变量
        let dataManager = window.doneLotsDataManager;
        let logContainer = document.getElementById('logOutput');
        
        // 日志功能
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            logContainer.textContent += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`%c${message}`, getLogStyle(type));
        }
        
        function getLogStyle(type) {
            const styles = {
                info: 'color: #007bff',
                success: 'color: #28a745; font-weight: bold',
                error: 'color: #dc3545; font-weight: bold',
                warning: 'color: #ffc107; font-weight: bold'
            };
            return styles[type] || styles.info;
        }
        
        function clearLog() {
            logContainer.textContent = '日志已清空...\n';
        }
        
        // 注册事件监听器
        dataManager.on('modeChanged', (data) => {
            addLog(`模式切换: ${data.oldMode} -> ${data.newMode}`, 'info');
            updateModeStatus();
        });
        
        dataManager.on('filtersChanged', (filters) => {
            const activeFilters = Object.entries(filters)
                .filter(([key, value]) => value && value.toString().trim())
                .map(([key, value]) => `${key}=${value}`)
                .join(', ');
            
            addLog(`筛选条件更新: ${activeFilters || '无'}`, 'info');
            document.getElementById('filtersStatus').textContent = `筛选条件: ${activeFilters || '无'}`;
        });
        
        dataManager.on('loadingChanged', (data) => {
            const status = data.loading ? '加载中...' : '加载完成';
            addLog(`${data.mode}模式 ${status}`, data.loading ? 'warning' : 'success');
            
            // 更新按钮状态
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.disabled = data.loading;
            });
        });
        
        dataManager.on('dataLoaded', (data) => {
            addLog(`数据加载成功: ${data.mode}模式, ${data.records.length}条记录`, 'success');
            updateDataDisplay(data.records, data.pagination);
            updatePaginationInfo(data.pagination);
        });
        
        dataManager.on('failedLotsLoaded', (data) => {
            addLog(`失败批次加载成功: ${data.records.length}条记录`, 'success');
            updateFailedLotsStatus(data.summary);
            updateDataDisplay(data.records, null, 'failed');
        });
        
        dataManager.on('dataLoadError', (data) => {
            addLog(`数据加载失败: ${data.mode}模式, ${data.error}`, 'error');
            document.getElementById('dataDisplay').innerHTML = `<p style="color: red;">加载失败: ${data.error}</p>`;
        });
        
        // 界面更新函数
        function updateModeStatus() {
            const mode = dataManager.getCurrentMode();
            const modeNames = {
                'view': '查看模式',
                'adjust': '调整模式',
                'final_result': '最终结果模式'
            };
            document.getElementById('modeStatus').textContent = `当前模式: ${modeNames[mode]} (${mode})`;
        }
        
        function updatePaginationInfo(pagination) {
            if (pagination) {
                const info = `第${pagination.page}页 / 共${pagination.total_pages}页，每页${pagination.size}条，总计${pagination.total}条记录`;
                document.getElementById('paginationInfo').textContent = `分页信息: ${info}`;
            }
        }
        
        function updateFailedLotsStatus(summary) {
            if (summary) {
                const status = `总失败: ${summary.total_failed}, 配置缺失: ${summary.config_missing}, 设备不兼容: ${summary.equipment_incompatible}, 其他: ${summary.other_reasons}`;
                document.getElementById('failedLotsStatus').textContent = `失败批次统计: ${status}`;
            }
        }
        
        function updateDataDisplay(records, pagination = null, type = 'main') {
            const container = document.getElementById('dataDisplay');
            
            if (!records || records.length === 0) {
                container.innerHTML = '<p>没有数据</p>';
                return;
            }
            
            // 构建表格
            let html = '<table class="data-table"><thead><tr>';
            
            if (type === 'failed') {
                // 失败批次数据结构
                html += '<th>批次号</th><th>产品名称</th><th>工序</th><th>良品数量</th><th>失败原因</th><th>建议</th><th>时间</th>';
                html += '</tr></thead><tbody>';
                
                records.forEach(record => {
                    html += `<tr>
                        <td>${record.LOT_ID || ''}</td>
                        <td>${record.DEVICE || ''}</td>
                        <td>${record.STAGE || ''}</td>
                        <td>${record.GOOD_QTY || 0}</td>
                        <td title="${record.failure_reason || ''}">${(record.failure_reason || '').substring(0, 30)}...</td>
                        <td title="${record.suggestion || ''}">${(record.suggestion || '').substring(0, 30)}...</td>
                        <td>${record.timestamp ? new Date(record.timestamp).toLocaleString() : ''}</td>
                    </tr>`;
                });
            } else {
                // 主数据结构
                html += '<th>优先级</th><th>批次号</th><th>产品名称</th><th>分选机ID</th><th>良品数量</th><th>工序</th><th>状态</th><th>创建时间</th>';
                html += '</tr></thead><tbody>';
                
                records.forEach(record => {
                    html += `<tr>
                        <td>${record.PRIORITY || ''}</td>
                        <td>${record.LOT_ID || ''}</td>
                        <td>${record.DEVICE || ''}</td>
                        <td>${record.HANDLER_ID || ''}</td>
                        <td>${record.GOOD_QTY || 0}</td>
                        <td>${record.STAGE || ''}</td>
                        <td>${record.WIP_STATE || ''}</td>
                        <td>${record.CREATE_TIME || ''}</td>
                    </tr>`;
                });
            }
            
            html += '</tbody></table>';
            container.innerHTML = html;
        }
        
        // 操作函数
        function changeMode() {
            const mode = document.getElementById('modeSelect').value;
            dataManager.setMode(mode);
        }
        
        function applyFilters() {
            const filters = {
                search: document.getElementById('searchInput').value.trim(),
                lot_id: document.getElementById('lotIdInput').value.trim(),
                device: document.getElementById('deviceInput').value.trim(),
                handler_id: document.getElementById('handlerIdInput').value.trim()
            };
            
            const priorityMin = document.getElementById('priorityMin').value;
            const priorityMax = document.getElementById('priorityMax').value;
            if (priorityMin && priorityMax) {
                filters.priority_min = priorityMin;
                filters.priority_max = priorityMax;
            }
            
            dataManager.applyFilters(filters);
        }
        
        function clearFilters() {
            // 清空输入框
            document.getElementById('searchInput').value = '';
            document.getElementById('lotIdInput').value = '';
            document.getElementById('deviceInput').value = '';
            document.getElementById('handlerIdInput').value = '';
            document.getElementById('priorityMin').value = '';
            document.getElementById('priorityMax').value = '';
            
            dataManager.clearFilters();
        }
        
        function goToPage() {
            const page = parseInt(document.getElementById('pageInput').value) || 1;
            dataManager.goToPage(page);
        }
        
        function changePageSize() {
            const size = parseInt(document.getElementById('pageSizeSelect').value) || 50;
            dataManager.changePageSize(size);
        }
        
        function loadData() {
            dataManager.loadData();
        }
        
        function refreshData() {
            dataManager.refresh();
        }
        
        function applySort() {
            const field = document.getElementById('sortField').value;
            const order = document.getElementById('sortOrder').value;
            document.getElementById('sortStatus').textContent = `排序: ${field} ${order}`;
            dataManager.applySort(field, order);
        }
        
        function loadFailedLots() {
            const hours = parseInt(document.getElementById('failedHours').value);
            const currentOnly = document.getElementById('failedCurrentOnly').value === 'true';
            
            dataManager.loadFailedLots({ currentOnly, hours });
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('🚀 DoneLotsDataManager 测试页面初始化完成', 'success');
            updateModeStatus();
            
            // 自动加载初始数据
            setTimeout(() => {
                addLog('📊 自动加载初始数据...', 'info');
                loadData();
            }, 1000);
        });
    </script>
</body>
</html>