<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DoneLotsFilterUI 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-section h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .data-display {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }
        
        .data-table th {
            background: #f8f9fa;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #f8f9fa;
            vertical-align: top;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .pagination-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .pagination-info {
            font-weight: 600;
            color: #495057;
        }
        
        .pagination-buttons {
            display: flex;
            gap: 10px;
        }
        
        .page-btn {
            padding: 8px 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .page-btn:hover {
            background: #0056b3;
        }
        
        .page-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .loading-indicator {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }
        
        .loading-indicator.active {
            color: #007bff;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .log-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info {
            color: #17a2b8;
        }
        
        .log-entry.success {
            color: #28a745;
            font-weight: bold;
        }
        
        .log-entry.error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .log-entry.warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 8px;
            }
            
            .content {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            
            .pagination-controls {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }
            
            .pagination-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 已排产表筛选UI组件测试</h1>
            <p>测试DoneLotsFilterUI与DoneLotsDataManager的集成功能</p>
        </div>
        
        <div class="content">
            <!-- 筛选UI容器 -->
            <div class="demo-section">
                <h3>📋 筛选面板</h3>
                <div id="filterContainer"></div>
            </div>
            
            <!-- 数据统计 -->
            <div class="demo-section">
                <h3>📊 数据统计</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="totalRecords">0</div>
                        <div class="stat-label">总记录数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="currentPage">1</div>
                        <div class="stat-label">当前页码</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="totalPages">0</div>
                        <div class="stat-label">总页数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="activeFilters">0</div>
                        <div class="stat-label">活跃筛选</div>
                    </div>
                </div>
            </div>
            
            <!-- 分页控制 -->
            <div class="demo-section">
                <div class="pagination-controls">
                    <div class="pagination-info" id="paginationInfo">
                        暂无数据
                    </div>
                    <div class="pagination-buttons">
                        <button class="page-btn" id="firstPageBtn" onclick="goToFirstPage()">首页</button>
                        <button class="page-btn" id="prevPageBtn" onclick="goToPrevPage()">上一页</button>
                        <button class="page-btn" id="nextPageBtn" onclick="goToNextPage()">下一页</button>
                        <button class="page-btn" id="lastPageBtn" onclick="goToLastPage()">末页</button>
                    </div>
                </div>
            </div>
            
            <!-- 数据展示 -->
            <div class="demo-section">
                <h3>📄 数据展示</h3>
                <div id="dataDisplay" class="data-display">
                    <div class="loading-indicator" id="loadingIndicator">
                        📊 准备加载数据...
                    </div>
                </div>
            </div>
            
            <!-- 操作日志 -->
            <div class="demo-section">
                <h3>📝 操作日志</h3>
                <div id="logPanel" class="log-panel"></div>
                <div style="margin-top: 10px; text-align: center;">
                    <button class="page-btn" onclick="clearLog()">清空日志</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 加载依赖 -->
    <script src="/static/js/done_lots_data_manager.js"></script>
    <script src="/static/js/done_lots_filter_ui.js"></script>
    
    <script>
        // 全局变量
        let dataManager = window.doneLotsDataManager;
        let filterUI = null;
        let currentPagination = { page: 1, size: 50, total: 0, total_pages: 0 };
        
        // 日志功能
        function addLog(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
            
            // 限制日志条数
            if (logPanel.children.length > 100) {
                logPanel.removeChild(logPanel.firstChild);
            }
            
            console.log(`%c${message}`, getLogStyle(type));
        }
        
        function getLogStyle(type) {
            const styles = {
                info: 'color: #17a2b8',
                success: 'color: #28a745; font-weight: bold',
                error: 'color: #dc3545; font-weight: bold',
                warning: 'color: #ffc107; font-weight: bold'
            };
            return styles[type] || styles.info;
        }
        
        function clearLog() {
            document.getElementById('logPanel').innerHTML = '';
            addLog('日志已清空', 'info');
        }
        
        // 更新统计信息
        function updateStats(data) {
            if (data && data.pagination) {
                document.getElementById('totalRecords').textContent = data.pagination.total || 0;
                document.getElementById('currentPage').textContent = data.pagination.page || 1;
                document.getElementById('totalPages').textContent = data.pagination.total_pages || 0;
                currentPagination = data.pagination;
            }
            
            const filters = dataManager.getFilters();
            const activeFilterCount = Object.values(filters).filter(v => v && v.toString().trim()).length;
            document.getElementById('activeFilters').textContent = activeFilterCount;
        }
        
        // 更新分页信息
        function updatePaginationInfo(pagination) {
            if (!pagination) return;
            
            const info = `第 ${pagination.page} 页，共 ${pagination.total_pages} 页 | 每页 ${pagination.size} 条，共 ${pagination.total} 条记录`;
            document.getElementById('paginationInfo').textContent = info;
            
            // 更新按钮状态
            document.getElementById('firstPageBtn').disabled = pagination.page <= 1;
            document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
            document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.total_pages;
            document.getElementById('lastPageBtn').disabled = pagination.page >= pagination.total_pages;
        }
        
        // 更新数据显示
        function updateDataDisplay(records, isLoading = false) {
            const container = document.getElementById('dataDisplay');
            
            if (isLoading) {
                container.innerHTML = '<div class="loading-indicator active">📊 加载中...</div>';
                return;
            }
            
            if (!records || records.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📭</div>
                        <h4>暂无数据</h4>
                        <p>请尝试调整筛选条件或检查数据源</p>
                    </div>
                `;
                return;
            }
            
            // 构建数据表格
            let html = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th style="width: 80px;">优先级</th>
                            <th style="width: 120px;">批次号</th>
                            <th style="width: 200px;">产品名称</th>
                            <th style="width: 120px;">分选机ID</th>
                            <th style="width: 80px;">良品数量</th>
                            <th style="width: 100px;">工序</th>
                            <th style="width: 80px;">状态</th>
                            <th style="width: 150px;">创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            records.forEach((record, index) => {
                html += `
                    <tr>
                        <td>${record.PRIORITY || '-'}</td>
                        <td title="${record.LOT_ID || ''}">${(record.LOT_ID || '').substring(0, 15)}${(record.LOT_ID || '').length > 15 ? '...' : ''}</td>
                        <td title="${record.DEVICE || ''}">${(record.DEVICE || '').substring(0, 25)}${(record.DEVICE || '').length > 25 ? '...' : ''}</td>
                        <td>${record.HANDLER_ID || '-'}</td>
                        <td style="text-align: right;">${(record.GOOD_QTY || 0).toLocaleString()}</td>
                        <td>${record.STAGE || '-'}</td>
                        <td>
                            <span style="
                                background: ${getStatusColor(record.WIP_STATE)};
                                color: white;
                                padding: 2px 8px;
                                border-radius: 12px;
                                font-size: 11px;
                            ">${record.WIP_STATE || 'UNKNOWN'}</span>
                        </td>
                        <td>${formatDateTime(record.CREATE_TIME)}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }
        
        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'WAIT': '#6c757d',
                'PROCESSING': '#007bff',
                'COMPLETED': '#28a745',
                'FAILED': '#dc3545',
                'PAUSED': '#ffc107'
            };
            return colors[status] || '#6c757d';
        }
        
        // 格式化时间
        function formatDateTime(dateTime) {
            if (!dateTime) return '-';
            
            try {
                const date = new Date(dateTime);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return dateTime.toString().substring(0, 16);
            }
        }
        
        // 分页控制函数
        function goToFirstPage() {
            if (currentPagination.page > 1) {
                dataManager.goToPage(1);
            }
        }
        
        function goToPrevPage() {
            if (currentPagination.page > 1) {
                dataManager.goToPage(currentPagination.page - 1);
            }
        }
        
        function goToNextPage() {
            if (currentPagination.page < currentPagination.total_pages) {
                dataManager.goToPage(currentPagination.page + 1);
            }
        }
        
        function goToLastPage() {
            if (currentPagination.page < currentPagination.total_pages) {
                dataManager.goToPage(currentPagination.total_pages);
            }
        }
        
        // 事件监听器设置
        function setupEventListeners() {
            // 数据管理器事件
            dataManager.on('loadingChanged', (data) => {
                addLog(`${data.mode} 模式 ${data.loading ? '开始加载' : '加载完成'}`, 'info');
                updateDataDisplay(null, data.loading);
            });
            
            dataManager.on('dataLoaded', (data) => {
                addLog(`数据加载成功: ${data.records.length} 条记录`, 'success');
                updateDataDisplay(data.records);
                updateStats(data);
                updatePaginationInfo(data.pagination);
                
                if (data.performance) {
                    addLog(`查询性能: 耗时 ${data.performance.query_time || 'N/A'}ms`, 'info');
                }
            });
            
            dataManager.on('dataLoadError', (data) => {
                addLog(`数据加载失败: ${data.error}`, 'error');
                updateDataDisplay([]);
            });
            
            dataManager.on('filtersChanged', (filters) => {
                const activeCount = Object.values(filters).filter(v => v && v.toString().trim()).length;
                addLog(`筛选条件已更新: ${activeCount} 个活跃条件`, 'info');
                updateStats({ pagination: currentPagination });
            });
            
            dataManager.on('modeChanged', (data) => {
                addLog(`模式切换: ${data.oldMode} → ${data.newMode}`, 'warning');
            });
        }
        
        // 初始化应用
        function initializeApp() {
            try {
                addLog('🚀 初始化应用...', 'info');
                
                // 创建筛选UI
                filterUI = new DoneLotsFilterUI('filterContainer', dataManager);
                addLog('✅ 筛选UI创建成功', 'success');
                
                // 设置事件监听器
                setupEventListeners();
                addLog('✅ 事件监听器设置完成', 'success');
                
                // 初始化数据加载
                setTimeout(() => {
                    addLog('📊 开始加载初始数据...', 'info');
                    dataManager.loadData();
                }, 1000);
                
                addLog('🎉 应用初始化完成', 'success');
                
            } catch (error) {
                addLog(`❌ 应用初始化失败: ${error.message}`, 'error');
                console.error('应用初始化失败:', error);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            addLog('📄 页面DOM加载完成', 'info');
            initializeApp();
        });
        
        // 添加一些测试函数到全局作用域
        window.testQuickSearch = function() {
            const searchInput = document.getElementById('quickSearch');
            if (searchInput) {
                searchInput.value = 'YX';
                filterUI.applyQuickSearch();
            }
        };
        
        window.testAdvancedFilter = function() {
            const filters = {
                device: 'JWH',
                priority_min: '1',
                priority_max: '100'
            };
            
            Object.entries(filters).forEach(([key, value]) => {
                const element = document.getElementById(`filter_${key}`);
                if (element) element.value = value;
            });
            
            filterUI.applyFilters();
        };
        
        // 提供全局访问
        window.addLog = addLog;
        window.filterUI = filterUI;
    </script>
</body>
</html>