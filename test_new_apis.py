#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新添加的API接口
验证失败批次和最终结果数据的API
"""

# 1. 编码修复 (必须在最开头)
import sys
import io
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 2. 基础导入
import os
import logging
import json
from datetime import datetime

# 3. 路径设置 (在其他导入之前)
os.chdir(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 4. 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)-8s | %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_new_apis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('TestNewAPIs')

def test_new_apis():
    """测试新添加的API接口"""
    try:
        logger.info("🧪 开始测试新添加的API接口...")
        
        # 5. Flask应用创建 (标准模式)
        from app import create_app
        app, socketio = create_app()
        
        with app.app_context():
            with app.test_client() as client:
                logger.info("✅ Flask应用和测试客户端创建成功")
                
                # 测试1: 失败批次数据API（当前数据）
                logger.info("🧪 测试1: 失败批次数据API（当前24小时数据）")
                response1 = client.get('/api/v2/production/done-lots/failed?current_only=true&hours=24')
                
                if response1.status_code == 200:
                    data1 = response1.get_json()
                    if data1.get('success'):
                        failed_lots = data1['data']['failed_lots']
                        summary = data1['data']['summary']
                        logger.info(f"✅ 失败批次查询成功: {len(failed_lots)}条记录")
                        logger.info(f"📊 失败统计: 总计={summary['total_failed']}, 配置缺失={summary['config_missing']}, 设备不兼容={summary['equipment_incompatible']}, 其他={summary['other_reasons']}")
                        if failed_lots:
                            sample = failed_lots[0]
                            logger.info(f"📊 失败示例: LOT_ID={sample.get('LOT_ID')}, 失败原因={sample.get('failure_reason')}")
                    else:
                        logger.error(f"❌ 失败批次查询失败: {data1.get('message')}")
                else:
                    logger.error(f"❌ 失败批次查询HTTP错误: {response1.status_code}")
                
                # 测试2: 失败批次数据API（历史数据）
                logger.info("🧪 测试2: 失败批次数据API（历史数据）")
                response2 = client.get('/api/v2/production/done-lots/failed?current_only=false')
                
                if response2.status_code == 200:
                    data2 = response2.get_json()
                    if data2.get('success'):
                        failed_lots = data2['data']['failed_lots']
                        logger.info(f"✅ 历史失败批次查询成功: {len(failed_lots)}条记录")
                        if 'performance' in data2:
                            perf = data2['performance']
                            logger.info(f"⚡ 性能信息: 查询耗时={perf.get('query_time', 'N/A')}ms")
                    else:
                        logger.error(f"❌ 历史失败批次查询失败: {data2.get('message')}")
                else:
                    logger.error(f"❌ 历史失败批次查询HTTP错误: {response2.status_code}")
                
                # 测试3: 失败批次数据API（带筛选条件）
                logger.info("🧪 测试3: 失败批次数据API（带筛选条件）")
                response3 = client.get('/api/v2/production/done-lots/failed?current_only=true&search=配置&hours=168')
                
                if response3.status_code == 200:
                    data3 = response3.get_json()
                    if data3.get('success'):
                        failed_lots = data3['data']['failed_lots']
                        logger.info(f"✅ 带筛选的失败批次查询成功: {len(failed_lots)}条记录")
                        if data3.get('debug_info'):
                            debug = data3['debug_info']
                            logger.info(f"📊 调试信息: 数据源={debug.get('data_source')}, 筛选条件数={debug.get('filters_applied')}")
                    else:
                        logger.error(f"❌ 带筛选的失败批次查询失败: {data3.get('message')}")
                else:
                    logger.error(f"❌ 带筛选的失败批次查询HTTP错误: {response3.status_code}")
                
                # 测试4: 最终结果数据API（基础查询）
                logger.info("🧪 测试4: 最终结果数据API（基础查询）")
                response4 = client.get('/api/v2/production/done-lots/final-result')
                
                if response4.status_code == 200:
                    data4 = response4.get_json()
                    if data4.get('success'):
                        records = data4.get('data', [])
                        pagination = data4.get('pagination', {})
                        logger.info(f"✅ 最终结果查询成功: {len(records)}条记录，总数: {pagination.get('total', 0)}")
                        if records:
                            sample = records[0]
                            logger.info(f"📊 最终结果示例: LOT_ID={sample.get('LOT_ID')}, DEVICE={sample.get('DEVICE')}")
                        elif data4.get('message'):
                            logger.info(f"ℹ️ 最终结果查询消息: {data4.get('message')}")
                    else:
                        logger.error(f"❌ 最终结果查询失败: {data4.get('message')}")
                else:
                    logger.error(f"❌ 最终结果查询HTTP错误: {response4.status_code}")
                
                # 测试5: 最终结果数据API（分页查询）
                logger.info("🧪 测试5: 最终结果数据API（分页查询）")
                response5 = client.get('/api/v2/production/done-lots/final-result?page=1&size=10&sort_by=CREATE_TIME&sort_order=DESC')
                
                if response5.status_code == 200:
                    data5 = response5.get_json()
                    if data5.get('success'):
                        records = data5.get('data', [])
                        pagination = data5.get('pagination', {})
                        filters = data5.get('filters', {})
                        logger.info(f"✅ 最终结果分页查询成功: 第{pagination.get('page')}页，{len(records)}条记录")
                        logger.info(f"📊 分页信息: 总数={pagination.get('total')}, 排序字段={filters.get('sort_by')}")
                    else:
                        logger.error(f"❌ 最终结果分页查询失败: {data5.get('message')}")
                else:
                    logger.error(f"❌ 最终结果分页查询HTTP错误: {response5.status_code}")
                
                # 测试6: 最终结果数据API（搜索查询）
                logger.info("🧪 测试6: 最终结果数据API（搜索查询）")
                response6 = client.get('/api/v2/production/done-lots/final-result?search=L&size=5')
                
                if response6.status_code == 200:
                    data6 = response6.get_json()
                    if data6.get('success'):
                        records = data6.get('data', [])
                        filters = data6.get('filters', {})
                        logger.info(f"✅ 最终结果搜索查询成功: {len(records)}条记录，筛选条件数: {filters.get('filters_applied', 0)}")
                    else:
                        logger.error(f"❌ 最终结果搜索查询失败: {data6.get('message')}")
                else:
                    logger.error(f"❌ 最终结果搜索查询HTTP错误: {response6.status_code}")
                
                logger.info("🎉 新API接口测试完成！")
                return True
                
    except Exception as e:
        logger.error(f"❌ 测试执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """入口函数"""
    success = test_new_apis()
    print("🎉 测试: 通过" if success else "❌ 测试: 失败")

if __name__ == "__main__":
    main()